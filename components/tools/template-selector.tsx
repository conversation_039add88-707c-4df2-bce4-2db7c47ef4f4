'use client'

import { useState, useMemo } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Check, Palette, FileText, Building2, Minus, Sparkles, Crown, Briefcase, Zap, Star } from "lucide-react";
import { cn } from "@/lib/utils";
import { FontLoader, getTemplateFontClass, fontVariables } from "./template-fonts";

export type TemplateType = "modern" | "elegant" | "corporate" | "minimal" | "executive" | "creative" | "professional" | "premium";

export interface TemplateOption {
  id: TemplateType;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  preview: string;
  color: string;
  accentColor: string;
  features: string[];
  category: "standard" | "premium";
  popularity?: "popular" | "trending" | "new";
  fontFamily: {
    name: string;
    importName: string;
    weights: number[];
    style?: 'normal' | 'italic' | 'oblique';
  };
}

// Define font configurations
const fonts = {
  inter: {
    name: 'Inter',
    importName: 'Inter',
    weights: [400, 500, 600, 700]
  },
  playfair: {
    name: 'Playfair Display',
    importName: 'Playfair_Display',
    weights: [400, 500, 600, 700, 800]
  },
  lora: {
    name: 'Lora',
    importName: 'Lora',
    weights: [400, 500, 600, 700]
  },
  cormorant: {
    name: 'Cormorant Garamond',
    importName: 'Cormorant_Garamond',
    weights: [400, 500, 600, 700]
  },
  dmSerif: {
    name: 'DM Serif Text',
    importName: 'DM_Serif_Text',
    weights: [400],
    style: 'normal' as const
  }
};

const templates: TemplateOption[] = [
  {
    id: "modern",
    name: "Modern",
    description: "Clean and contemporary design with blue accents and modern typography",
    icon: Palette,
    preview: "/api/placeholder/200/280",
    color: "bg-blue-500",
    accentColor: "border-blue-200 bg-blue-50",
    features: ["Blue color scheme", "Modern typography", "Clean layout", "Professional headers"],
    category: "standard",
    popularity: "popular",
    fontFamily: fonts.inter
  },
  {
    id: "elegant",
    name: "Elegant",
    description: "Sophisticated and refined design with subtle styling and premium feel",
    icon: Crown,
    preview: "/api/placeholder/200/280",
    color: "bg-slate-600",
    accentColor: "border-slate-200 bg-slate-50",
    features: ["Refined design", "Premium typography", "Elegant spacing", "Subtle accents"],
    category: "premium",
    popularity: "trending",
    fontFamily: fonts.lora
  },
  {
    id: "corporate",
    name: "Corporate",
    description: "Business-focused design with green accents and professional structure",
    icon: Building2,
    preview: "/api/placeholder/200/280",
    color: "bg-emerald-600",
    accentColor: "border-emerald-200 bg-emerald-50",
    features: ["Corporate styling", "Green color scheme", "Business-ready", "Structured layout"],
    category: "standard",
    fontFamily: fonts.inter
  },
  {
    id: "minimal",
    name: "Minimal",
    description: "Ultra-clean black and white design for maximum clarity and focus",
    icon: Minus,
    preview: "/api/placeholder/200/280",
    color: "bg-gray-900",
    accentColor: "border-gray-200 bg-gray-50",
    features: ["Black & white", "Ultra-clean", "Maximum clarity", "Distraction-free"],
    category: "standard",
    fontFamily: fonts.inter
  },
  {
    id: "executive",
    name: "Executive",
    description: "Premium design for high-level business communications with gold accents",
    icon: Briefcase,
    preview: "/api/placeholder/200/280",
    color: "bg-amber-600",
    accentColor: "border-amber-200 bg-amber-50",
    features: ["Gold accents", "Executive styling", "Premium feel", "Luxury design"],
    category: "premium",
    popularity: "new",
    fontFamily: fonts.playfair
  },
  {
    id: "creative",
    name: "Creative",
    description: "Dynamic and innovative design with purple accents for creative professionals",
    icon: Sparkles,
    preview: "/api/placeholder/200/280",
    color: "bg-purple-600",
    accentColor: "border-purple-200 bg-purple-50",
    features: ["Purple accents", "Creative layout", "Dynamic design", "Innovative styling"],
    category: "standard",
    popularity: "trending",
    fontFamily: fonts.inter
  },
  {
    id: "professional",
    name: "Professional",
    description: "Classic business design with navy blue accents and traditional structure",
    icon: FileText,
    preview: "/api/placeholder/200/280",
    color: "bg-blue-800",
    accentColor: "border-blue-200 bg-blue-50",
    features: ["Navy blue theme", "Traditional layout", "Professional styling", "Classic design"],
    category: "standard",
    fontFamily: fonts.dmSerif
  },
  {
    id: "premium",
    name: "Premium",
    description: "Luxury design with sophisticated styling and premium typography",
    icon: Star,
    preview: "/api/placeholder/200/280",
    color: "bg-indigo-700",
    accentColor: "border-indigo-200 bg-indigo-50",
    features: ["Luxury styling", "Premium typography", "Sophisticated design", "High-end feel"],
    category: "premium",
    popularity: "popular",
    fontFamily: fonts.cormorant
  }
];

interface TemplateSelectorProps {
  selectedTemplate: TemplateType;
  onTemplateChange: (template: TemplateType) => void;
  className?: string;
}

export function TemplateSelector({
  selectedTemplate,
  onTemplateChange,
  className
}: TemplateSelectorProps) {
  const selectedTemplateData = useMemo(() => 
    templates.find(t => t.id === selectedTemplate),
    [selectedTemplate]
  );
  const [hoveredTemplate, setHoveredTemplate] = useState<TemplateType | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<"all" | "standard" | "premium">("all");
  const [expandedTemplates, setExpandedTemplates] = useState<Set<TemplateType>>(new Set());

  const toggleTemplateExpansion = (templateId: TemplateType) => {
    const newExpanded = new Set(expandedTemplates);
    if (newExpanded.has(templateId)) {
      newExpanded.delete(templateId);
    } else {
      newExpanded.add(templateId);
    }
    setExpandedTemplates(newExpanded);
  };

  const filteredTemplates = templates.filter(template =>
    selectedCategory === "all" || template.category === selectedCategory
  );

  const standardTemplates = templates.filter(t => t.category === "standard");
  const premiumTemplates = templates.filter(t => t.category === "premium");

  // Group templates by category
  const templatesByCategory = {
    standard: standardTemplates,
    premium: premiumTemplates
  };

  const renderTemplateCard = (template: TemplateOption) => {
    const Icon = template.icon;
    const isSelected = selectedTemplate === template.id;
    const isHovered = hoveredTemplate === template.id;
    const isExpanded = expandedTemplates.has(template.id);
    const isPremium = template.category === "premium";

    return (
      <Card
        key={template.id}
        className={cn(
          "cursor-pointer transition-all duration-300 relative overflow-hidden group",
          isSelected 
            ? "ring-2 ring-primary shadow-xl scale-[1.02] z-10" 
            : "hover:shadow-lg hover:-translate-y-1 hover:z-10",
          isPremium 
            ? "border-amber-200 bg-gradient-to-br from-white to-amber-50/50 hover:shadow-amber-100/50" 
            : "border-border",
          isPremium && isHovered && "hover:scale-[1.02]"
        )}
        onMouseEnter={() => setHoveredTemplate(template.id)}
        onMouseLeave={() => setHoveredTemplate(null)}
        onClick={() => onTemplateChange(template.id)}
      >
        <CardContent className="p-0">
          <div className="space-y-0">
            {/* Template Preview */}
            <div className="relative aspect-[3/4] bg-gradient-to-br from-gray-50 to-gray-100 overflow-hidden">
              {/* Background Pattern */}
              <div className={cn(
                "absolute inset-0",
                template.accentColor,
                "opacity-20"
              )} />

              {/* Premium Badge */}
              {isPremium && (
                <div className="absolute top-2 left-2 z-10">
                  <Badge className="bg-gradient-to-r from-amber-500 to-orange-500 text-white text-xs px-2 py-0.5 rounded-full shadow-md">
                    <Crown className="h-3 w-3 mr-1" />
                    Premium
                  </Badge>
                </div>
              )}

              {/* Standard Badge */}
              {!isPremium && (
                <div className="absolute top-2 left-2 z-10">
                  <Badge variant="secondary" className="text-xs px-2 py-0.5 rounded-full">
                    Standard
                  </Badge>
                </div>
              )}

              {/* Popularity Badge */}
              {template.popularity && (
                <div className="absolute top-2 right-2 z-10">
                  <Badge
                    className={cn(
                      "text-xs px-2 py-0.5 rounded-full shadow-sm",
                      template.popularity === "popular" && "bg-green-100 text-green-800 border-green-200",
                      template.popularity === "trending" && "bg-blue-100 text-blue-800 border-blue-200",
                      template.popularity === "new" && "bg-purple-100 text-purple-800 border-purple-200"
                    )}
                  >
                    {template.popularity === "popular" && "🔥 Popular"}
                    {template.popularity === "trending" && "📈 Trending"}
                    {template.popularity === "new" && "✨ New"}
                  </Badge>
                </div>
              )}

              {/* Mock document preview */}
              <div className="absolute inset-3 bg-white rounded-lg shadow-md p-3 space-y-2">
                {/* Header */}
                <div className="flex items-center justify-between">
                  <div className={cn("h-3 rounded-full w-16", template.color)} />
                  <div className="text-right space-y-1">
                    <div className="h-2 bg-gray-300 rounded w-12" />
                    <div className="h-1 bg-gray-200 rounded w-8" />
                  </div>
                </div>

                {/* Company Info */}
                <div className="space-y-1">
                  <div className="h-1.5 bg-gray-800 rounded w-20" />
                  <div className="h-1 bg-gray-400 rounded w-16" />
                  <div className="h-1 bg-gray-400 rounded w-14" />
                </div>

                {/* Items Table */}
                <div className="space-y-1 mt-3">
                  <div className="flex justify-between items-center py-1 border-b border-gray-100">
                    <div className="h-1 bg-gray-600 rounded w-12" />
                    <div className="h-1 bg-gray-400 rounded w-8" />
                  </div>
                  <div className="flex justify-between items-center py-1">
                    <div className="h-1 bg-gray-400 rounded w-16" />
                    <div className="h-1 bg-gray-400 rounded w-6" />
                  </div>
                  <div className="flex justify-between items-center py-1">
                    <div className="h-1 bg-gray-400 rounded w-14" />
                    <div className="h-1 bg-gray-400 rounded w-8" />
                  </div>
                </div>

                {/* Total */}
                <div className="flex justify-end mt-2">
                  <div className={cn(
                    "h-2 rounded w-12",
                    template.color,
                    "opacity-80"
                  )} />
                </div>
              </div>

              {/* Selection indicator */}
              {isSelected && (
                <div className="absolute inset-0 bg-primary/5 flex items-center justify-center">
                  <div className="bg-primary text-primary-foreground rounded-full p-2 shadow-lg">
                    <Check className="h-4 w-4" />
                  </div>
                </div>
              )}
            </div>

            {/* Template Info */}
            <div className="p-4 space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Icon className={cn(
                    "h-4 w-4",
                    isSelected ? "text-primary" : (isPremium ? "text-amber-600" : "text-muted-foreground")
                  )} />
                  <span className={cn(
                    "font-semibold text-sm font-template-preview",
                    isSelected ? "text-primary" : "text-foreground",
                    getTemplateFontClass(template)
                  )}>
                    {template.name}
                  </span>
                </div>
                {isSelected && (
                  <Badge variant="default" className="text-xs">
                    Selected
                  </Badge>
                )}
              </div>

              <p className="text-xs text-muted-foreground line-clamp-2 leading-relaxed font-template-preview" style={{
                fontFamily: `var(${fontVariables[template.fontFamily.name as keyof typeof fontVariables] || '--font-sans'})`
              }}>
                {template.description}
              </p>

              {/* Features */}
              <div className="space-y-2">
                <div className="flex flex-wrap gap-1">
                  {template.features.slice(0, 2).map((feature, index) => (
                    <Badge
                      key={index}
                      variant="outline"
                      className={cn(
                        "text-xs px-2 py-0.5 border-current",
                        isPremium && "text-amber-600 border-amber-300 bg-amber-50/50"
                      )}
                    >
                      {isPremium && index === 0 && '✨ '}
                      {feature}
                    </Badge>
                  ))}
                  {template.features.length > 2 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-xs h-6 px-2 py-0.5 text-muted-foreground hover:text-foreground"
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleTemplateExpansion(template.id);
                      }}
                    >
                      {isExpanded ? 'Show less' : `+${template.features.length - 2} more`}
                    </Button>
                  )}
                </div>

                {/* Expanded features */}
                {isExpanded && template.features.length > 2 && (
                  <div className="space-y-1 pt-1">
                    {template.features.slice(2).map((feature, index) => (
                      <div key={index} className="flex items-start gap-1.5 text-xs text-muted-foreground">
                        <Check className="h-3.5 w-3.5 text-green-500 mt-0.5 flex-shrink-0" />
                        <span>{feature}</span>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* View Details Button */}
              <Button
                variant="outline"
                size="sm"
                className="w-full mt-2 text-xs"
                onClick={(e) => {
                  e.stopPropagation();
                  toggleTemplateExpansion(template.id);
                }}
              >
                {isExpanded ? 'Hide Details' : 'View Details'}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <FontLoader templates={templates}>
      <div className={cn("space-y-8", className)}>
        {selectedTemplateData && (
          <style jsx global>{`
            :root {
              --font-template: ${fontVariables[selectedTemplateData.fontFamily.name as keyof typeof fontVariables]};
            }
            
            .font-template {
              font-family: var(--font-template, var(--font-sans));
            }
            
            .font-template-preview {
              font-family: var(--font-template, var(--font-sans));
              font-weight: 500;
              letter-spacing: 0.01em;
            }
          `}</style>
        )}
      {/* Header */}
      <div className="text-center space-y-2">
        <h2 className="text-3xl font-bold tracking-tight">Choose Your Template</h2>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Select from our collection of professionally designed templates. Premium templates include additional features and styling options.
        </p>
      </div>

      {/* Category Filter */}
      <div className="flex justify-center">
        <div className="inline-flex items-center rounded-xl bg-muted p-1.5 gap-1">
          <Button
            variant={selectedCategory === "all" ? "default" : "ghost"}
            size="sm"
            onClick={() => setSelectedCategory("all")}
            className="rounded-lg px-4"
          >
            All Templates
            <span className="ml-2 bg-primary/10 text-primary rounded-full px-2 py-0.5 text-xs">
              {templates.length}
            </span>
          </Button>
          <Button
            variant={selectedCategory === "standard" ? "default" : "ghost"}
            size="sm"
            onClick={() => setSelectedCategory("standard")}
            className="rounded-lg px-4"
          >
            Standard
            <span className="ml-2 bg-muted-foreground/10 text-foreground rounded-full px-2 py-0.5 text-xs">
              {standardTemplates.length}
            </span>
          </Button>
          <Button
            variant={selectedCategory === "premium" ? "default" : "ghost"}
            size="sm"
            onClick={() => setSelectedCategory("premium")}
            className="rounded-lg px-4 bg-gradient-to-r from-amber-500/10 to-orange-500/10 hover:from-amber-500/20 hover:to-orange-500/20 text-amber-700 hover:text-amber-800"
          >
            <Crown className="h-3.5 w-3.5 mr-1.5" />
            Premium
            <span className="ml-2 bg-amber-500/20 text-amber-700 rounded-full px-2 py-0.5 text-xs">
              {premiumTemplates.length}
            </span>
          </Button>
        </div>
      </div>

      {/* Templates Grid */}
      <div className="space-y-10">
        {/* Standard Templates Section */}
        {(selectedCategory === "all" || selectedCategory === "standard") && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold uppercase tracking-wide text-muted-foreground flex items-center">
              <span className="h-px flex-1 bg-border mr-3"></span>
              Standard Templates
              <span className="ml-3 text-sm font-normal bg-muted px-2 py-0.5 rounded-full">
                {standardTemplates.length} templates
              </span>
              <span className="h-px flex-1 bg-border ml-3"></span>
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {standardTemplates.map(template => renderTemplateCard(template))}
            </div>
          </div>
        )}

        {/* Premium Templates Section */}
        {(selectedCategory === "all" || selectedCategory === "premium") && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold uppercase tracking-wide text-muted-foreground flex items-center">
              <span className="h-px flex-1 bg-gradient-to-r from-transparent via-amber-500/30 to-transparent mr-3"></span>
              <span className="flex items-center">
                <Crown className="h-4 w-4 mr-2 text-amber-500" />
                Premium Templates
              </span>
              <span className="ml-3 text-sm font-normal bg-amber-500/10 text-amber-700 px-2 py-0.5 rounded-full">
                {premiumTemplates.length} templates
              </span>
              <span className="h-px flex-1 bg-gradient-to-r from-transparent via-amber-500/30 to-transparent ml-3"></span>
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {premiumTemplates.map(template => renderTemplateCard(template))}
            </div>
          </div>
        )}
      </div>

      {/* Selected template detailed info */}
      {selectedTemplate && (
        <div className="bg-gradient-to-r from-primary/5 to-primary/10 border border-primary/20 rounded-xl p-6">
          <div className="flex items-start gap-4">
            <div className={cn(
              "w-12 h-12 rounded-lg flex items-center justify-center",
              templates.find(t => t.id === selectedTemplate)?.color
            )}>
              {(() => {
                const template = templates.find(t => t.id === selectedTemplate);
                const Icon = template?.icon;
                return Icon ? <Icon className="h-6 w-6 text-white" /> : null;
              })()}
            </div>
            <div className="flex-1 space-y-2">
              <div className="flex items-center gap-3">
                <h4 className="font-semibold text-lg">
                  {templates.find(t => t.id === selectedTemplate)?.name} Template
                </h4>
                {templates.find(t => t.id === selectedTemplate)?.category === "premium" && (
                  <Badge className="bg-gradient-to-r from-amber-500 to-orange-500 text-white">
                    <Crown className="h-3 w-3 mr-1" />
                    Premium
                  </Badge>
                )}
              </div>
              <p className="text-muted-foreground">
                {templates.find(t => t.id === selectedTemplate)?.description}
              </p>
              <div className="flex flex-wrap gap-2 mt-3">
                {templates.find(t => t.id === selectedTemplate)?.features.map((feature, index) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {feature}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
      </div>
    </FontLoader>
  );
}
