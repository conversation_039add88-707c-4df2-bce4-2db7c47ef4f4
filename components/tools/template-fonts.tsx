'use client';

import { Inter, Playfair_Display, <PERSON>ra, Corm<PERSON><PERSON>_<PERSON><PERSON>, DM_Serif_Text } from 'next/font/google';
import { useEffect, useState } from 'react';
import { TemplateOption } from './template-selector';

// Define font configurations with Next.js font loader
export const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
});

export const playfair = Playfair_Display({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-playfair',
});

export const lora = Lora({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-lora',
});

export const cormorant = Cormorant_Garamond({
  weight: ['400', '500', '600', '700'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-cormorant',
});

export const dmSerif = DM_Serif_Text({
  weight: '400',
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-dm-serif',
});

// Map of font names to their CSS variables
export const fontVariables = {
  'Inter': inter.variable,
  'Playfair Display': playfair.variable,
  'Lora': lora.variable,
  'Cormorant Garamond': cormorant.variable,
  'DM Serif Text': dmSerif.variable,
};

// Font loader component
interface FontLoaderProps {
  templates: TemplateOption[];
  children: React.ReactNode;
}

export function FontLoader({ templates, children }: FontLoaderProps) {
  const [loadedFonts, setLoadedFonts] = useState<Set<string>>(new Set());
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    
    // Get unique font variables from templates
    const uniqueFonts = new Set(
      templates.map(t => t.fontFamily.name)
        .filter((name): name is keyof typeof fontVariables => name in fontVariables)
    );

    setLoadedFonts(prev => {
      const newFonts = new Set(prev);
      uniqueFonts.forEach(font => newFonts.add(font));
      return newFonts;
    });
  }, [templates]);

  if (!isMounted) return null;

  // Apply font variables to the document
  const fontStyles: React.CSSProperties & {
    '--font-template'?: string;
  } = {};
  
  const firstFont = Array.from(loadedFonts)[0] as keyof typeof fontVariables | undefined;
  if (firstFont && fontVariables[firstFont]) {
    fontStyles['--font-template'] = `var(${fontVariables[firstFont]})`;
  }

  // Convert loaded fonts to an array and filter out any undefined values
  const fontClasses = Array.from(loadedFonts)
    .map(fontName => fontVariables[fontName as keyof typeof fontVariables])
    .filter(Boolean) as string[];

  return (
    <div 
      className={fontClasses.join(' ')}
      style={fontStyles}
    >
      {children}
    </div>
  );
}

// Helper to get font class for a template
export function getTemplateFontClass(template: TemplateOption) {
  const fontName = template.fontFamily.name as keyof typeof fontVariables;
  return fontVariables[fontName] || '';
}
