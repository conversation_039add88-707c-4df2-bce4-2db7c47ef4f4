import { Document, Page, Text, View, StyleSheet, Image } from "@react-pdf/renderer";

// Professional Template Styles
const createStyles = (template: string = "modern") => {
  const baseStyles = {
    page: {
      padding: 40,
      fontSize: 11,
      fontFamily: "Helvetica",
      color: "#333333",
      lineHeight: 1.4,
    },
    header: {
      marginBottom: 30,
    },
    title: {
      fontSize: 28,
      fontWeight: "bold",
      marginBottom: 8,
      letterSpacing: 0.5,
    },
    subtitle: {
      fontSize: 14,
      color: "#666666",
      marginBottom: 20,
    },
    info: {
      marginBottom: 25,
    },
    row: {
      flexDirection: "row" as const,
      marginBottom: 4,
    },
    label: {
      width: 80,
      fontWeight: "bold",
      color: "#555555",
    },
    value: {
      flex: 1,
      color: "#333333",
    },
    section: {
      marginBottom: 25,
    },
    sectionTitle: {
      fontSize: 14,
      fontWeight: "bold",
      marginBottom: 12,
      paddingBottom: 6,
      textTransform: "uppercase" as const,
      letterSpacing: 0.5,
    },
    table: {
      marginBottom: 20,
    },
    tableRow: {
      flexDirection: "row" as const,
      paddingVertical: 8,
      paddingHorizontal: 12,
    },
    tableHeader: {
      fontWeight: "bold",
      fontSize: 10,
      textTransform: "uppercase" as const,
      letterSpacing: 0.5,
    },
    tableCell: {
      flex: 1,
    },
    tableCellRight: {
      flex: 1,
      textAlign: "right" as const,
    },
    total: {
      marginTop: 15,
      paddingTop: 15,
    },
    totalRow: {
      flexDirection: "row" as const,
      justifyContent: "flex-end" as const,
      marginBottom: 6,
    },
    totalLabel: {
      width: 100,
      textAlign: "right" as const,
      marginRight: 20,
    },
    totalValue: {
      width: 80,
      textAlign: "right" as const,
    },
    grandTotal: {
      fontSize: 14,
      fontWeight: "bold",
      paddingTop: 8,
    },
    notes: {
      marginTop: 30,
      padding: 15,
      backgroundColor: "#f8f9fa",
      borderRadius: 4,
    },
    footer: {
      position: "absolute" as const,
      bottom: 30,
      left: 40,
      right: 40,
      textAlign: "center" as const,
      fontSize: 9,
      color: "#888888",
    },
    companyInfo: {
      marginBottom: 20,
    },
    companyName: {
      fontSize: 18,
      fontWeight: "bold",
      marginBottom: 4,
    },
    companyDetails: {
      fontSize: 10,
      color: "#666666",
      lineHeight: 1.3,
    },
  };

  // Template-specific overrides
  const templateStyles = {
    modern: {
      ...baseStyles,
      title: {
        ...baseStyles.title,
        color: "#2563eb",
      },
      sectionTitle: {
        ...baseStyles.sectionTitle,
        color: "#2563eb",
        borderBottom: "2px solid #2563eb",
      },
      tableHeader: {
        ...baseStyles.tableHeader,
        backgroundColor: "#2563eb",
        color: "#ffffff",
      },
      tableRow: {
        ...baseStyles.tableRow,
        borderBottom: "1px solid #e5e7eb",
      },
    },
    elegant: {
      ...baseStyles,
      title: {
        ...baseStyles.title,
        color: "#1f2937",
        fontSize: 32,
      },
      sectionTitle: {
        ...baseStyles.sectionTitle,
        color: "#374151",
        borderBottom: "1px solid #d1d5db",
      },
      tableHeader: {
        ...baseStyles.tableHeader,
        backgroundColor: "#f3f4f6",
        color: "#374151",
      },
      tableRow: {
        ...baseStyles.tableRow,
        borderBottom: "1px solid #f3f4f6",
      },
    },
    corporate: {
      ...baseStyles,
      title: {
        ...baseStyles.title,
        color: "#059669",
      },
      sectionTitle: {
        ...baseStyles.sectionTitle,
        color: "#059669",
        borderBottom: "2px solid #059669",
      },
      tableHeader: {
        ...baseStyles.tableHeader,
        backgroundColor: "#059669",
        color: "#ffffff",
      },
      tableRow: {
        ...baseStyles.tableRow,
        borderBottom: "1px solid #e5e7eb",
      },
    },
    minimal: {
      ...baseStyles,
      title: {
        ...baseStyles.title,
        color: "#000000",
        fontSize: 24,
      },
      sectionTitle: {
        ...baseStyles.sectionTitle,
        color: "#000000",
        borderBottom: "1px solid #000000",
      },
      tableHeader: {
        ...baseStyles.tableHeader,
        backgroundColor: "#000000",
        color: "#ffffff",
      },
      tableRow: {
        ...baseStyles.tableRow,
        borderBottom: "1px solid #e5e7eb",
      },
    },
    executive: {
      ...baseStyles,
      page: {
        ...baseStyles.page,
        backgroundColor: "#fefefe",
      },
      title: {
        ...baseStyles.title,
        color: "#d97706",
        fontSize: 30,
        fontWeight: "bold",
      },
      sectionTitle: {
        ...baseStyles.sectionTitle,
        color: "#d97706",
        borderBottom: "2px solid #d97706",
        backgroundColor: "#fef3c7",
        padding: 8,
        marginBottom: 15,
      },
      tableHeader: {
        ...baseStyles.tableHeader,
        backgroundColor: "#d97706",
        color: "#ffffff",
        padding: 12,
      },
      tableRow: {
        ...baseStyles.tableRow,
        borderBottom: "1px solid #f3f4f6",
        paddingVertical: 10,
      },
      companyName: {
        ...baseStyles.companyName,
        color: "#d97706",
        fontSize: 20,
      },
    },
    creative: {
      ...baseStyles,
      title: {
        ...baseStyles.title,
        color: "#7c3aed",
        fontSize: 26,
      },
      sectionTitle: {
        ...baseStyles.sectionTitle,
        color: "#7c3aed",
        borderBottom: "2px solid #7c3aed",
        borderLeft: "4px solid #7c3aed",
        paddingLeft: 12,
      },
      tableHeader: {
        ...baseStyles.tableHeader,
        backgroundColor: "#7c3aed",
        color: "#ffffff",
      },
      tableRow: {
        ...baseStyles.tableRow,
        borderBottom: "1px solid #e5e7eb",
      },
      companyName: {
        ...baseStyles.companyName,
        color: "#7c3aed",
      },
    },
    professional: {
      ...baseStyles,
      title: {
        ...baseStyles.title,
        color: "#1e40af",
        fontSize: 26,
      },
      sectionTitle: {
        ...baseStyles.sectionTitle,
        color: "#1e40af",
        borderBottom: "1px solid #1e40af",
        textTransform: "none" as const,
        fontSize: 16,
      },
      tableHeader: {
        ...baseStyles.tableHeader,
        backgroundColor: "#1e40af",
        color: "#ffffff",
      },
      tableRow: {
        ...baseStyles.tableRow,
        borderBottom: "1px solid #e5e7eb",
      },
      companyName: {
        ...baseStyles.companyName,
        color: "#1e40af",
      },
    },
    premium: {
      ...baseStyles,
      page: {
        ...baseStyles.page,
        backgroundColor: "#fefefe",
        padding: 50,
      },
      title: {
        ...baseStyles.title,
        color: "#4338ca",
        fontSize: 32,
        letterSpacing: 1,
      },
      sectionTitle: {
        ...baseStyles.sectionTitle,
        color: "#4338ca",
        borderBottom: "3px solid #4338ca",
        backgroundColor: "#eef2ff",
        padding: 10,
        marginBottom: 20,
        fontSize: 16,
      },
      tableHeader: {
        ...baseStyles.tableHeader,
        backgroundColor: "#4338ca",
        color: "#ffffff",
        padding: 15,
        fontSize: 11,
      },
      tableRow: {
        ...baseStyles.tableRow,
        borderBottom: "1px solid #e0e7ff",
        paddingVertical: 12,
      },
      companyName: {
        ...baseStyles.companyName,
        color: "#4338ca",
        fontSize: 22,
      },
      total: {
        ...baseStyles.total,
        backgroundColor: "#f8fafc",
        padding: 20,
        borderRadius: 8,
        marginTop: 20,
      },
    },
  };

  return StyleSheet.create(templateStyles[template as keyof typeof templateStyles] || templateStyles.modern);
};

interface PDFTemplateProps {
  type: "invoice" | "quote";
  template?: "modern" | "elegant" | "corporate" | "minimal" | "executive" | "creative" | "professional" | "premium";
  number: string;
  date: string;
  dueDate?: string;
  validUntil?: string;
  from: {
    name: string;
    address: string;
    email: string;
    phone: string;
    logo?: string;
  };
  to: {
    name: string;
    address: string;
    email: string;
    phone: string;
  };
  items: Array<{
    description: string;
    quantity: number;
    rate: number;
    amount: number;
  }>;
  subtotal?: number;
  tax?: {
    rate: number;
    amount: number;
  };
  discount?: {
    rate: number;
    amount: number;
  };
  total: number;
  currency?: string;
  terms?: string;
  notes?: string;
}

export function PDFTemplate({
  type,
  template = "modern",
  number,
  date,
  dueDate,
  validUntil,
  from,
  to,
  items,
  subtotal,
  tax,
  discount,
  total,
  currency = "USD",
  terms,
  notes,
}: PDFTemplateProps) {
  const styles = createStyles(template);
  const currencySymbol = currency === "USD" ? "$" : currency === "EUR" ? "€" : currency === "GBP" ? "£" : "$";

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* Header Section */}
        <View style={styles.header}>
          <View style={{ flexDirection: "row", justifyContent: "space-between", alignItems: "flex-start" }}>
            <View style={styles.companyInfo}>
              <Text style={styles.companyName}>{from.name}</Text>
              <View style={styles.companyDetails}>
                <Text>{from.address}</Text>
                <Text>{from.email}</Text>
                <Text>{from.phone}</Text>
              </View>
            </View>
            <View style={{ textAlign: "right" }}>
              <Text style={styles.title}>
                {type === "invoice" ? "INVOICE" : "QUOTE"}
              </Text>
              <Text style={styles.subtitle}>#{number}</Text>
            </View>
          </View>
        </View>

        {/* Document Info */}
        <View style={styles.info}>
          <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
            <View style={{ flex: 1 }}>
              <View style={styles.row}>
                <Text style={styles.label}>Date:</Text>
                <Text style={styles.value}>{date}</Text>
              </View>
              {type === "invoice" && dueDate && (
                <View style={styles.row}>
                  <Text style={styles.label}>Due Date:</Text>
                  <Text style={styles.value}>{dueDate}</Text>
                </View>
              )}
              {type === "quote" && validUntil && (
                <View style={styles.row}>
                  <Text style={styles.label}>Valid Until:</Text>
                  <Text style={styles.value}>{validUntil}</Text>
                </View>
              )}
            </View>
            <View style={{ flex: 1 }}>
              <Text style={[styles.sectionTitle, { marginBottom: 8 }]}>Bill To</Text>
              <Text style={{ fontWeight: "bold", marginBottom: 4 }}>{to.name}</Text>
              <Text>{to.address}</Text>
              <Text>{to.email}</Text>
              <Text>{to.phone}</Text>
            </View>
          </View>
        </View>

        {/* Items Table */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Items</Text>
          <View style={styles.table}>
            <View style={[styles.tableRow, styles.tableHeader]}>
              <Text style={[styles.tableCell, { flex: 3 }]}>Description</Text>
              <Text style={[styles.tableCell, { flex: 1 }]}>Qty</Text>
              <Text style={[styles.tableCellRight, { flex: 1.5 }]}>Rate</Text>
              <Text style={[styles.tableCellRight, { flex: 1.5 }]}>Amount</Text>
            </View>
            {items.map((item, index) => (
              <View key={index} style={styles.tableRow}>
                <Text style={[styles.tableCell, { flex: 3 }]}>{item.description}</Text>
                <Text style={[styles.tableCell, { flex: 1 }]}>{item.quantity}</Text>
                <Text style={[styles.tableCellRight, { flex: 1.5 }]}>
                  {currencySymbol}{item.rate.toFixed(2)}
                </Text>
                <Text style={[styles.tableCellRight, { flex: 1.5 }]}>
                  {currencySymbol}{item.amount.toFixed(2)}
                </Text>
              </View>
            ))}
          </View>

          {/* Totals Section */}
          <View style={styles.total}>
            {subtotal && (
              <View style={styles.totalRow}>
                <Text style={styles.totalLabel}>Subtotal:</Text>
                <Text style={styles.totalValue}>{currencySymbol}{subtotal.toFixed(2)}</Text>
              </View>
            )}
            {discount && discount.amount > 0 && (
              <View style={styles.totalRow}>
                <Text style={styles.totalLabel}>
                  Discount ({discount.rate}%):
                </Text>
                <Text style={styles.totalValue}>-{currencySymbol}{discount.amount.toFixed(2)}</Text>
              </View>
            )}
            {tax && tax.amount > 0 && (
              <View style={styles.totalRow}>
                <Text style={styles.totalLabel}>
                  Tax ({tax.rate}%):
                </Text>
                <Text style={styles.totalValue}>{currencySymbol}{tax.amount.toFixed(2)}</Text>
              </View>
            )}
            <View style={[styles.totalRow, styles.grandTotal]}>
              <Text style={[styles.totalLabel, { fontWeight: "bold" }]}>Total:</Text>
              <Text style={[styles.totalValue, { fontWeight: "bold" }]}>
                {currencySymbol}{total.toFixed(2)}
              </Text>
            </View>
          </View>
        </View>

        {/* Terms & Conditions */}
        {terms && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Terms & Conditions</Text>
            <Text>{terms}</Text>
          </View>
        )}

        {/* Notes */}
        {notes && (
          <View style={styles.notes}>
            <Text style={[styles.sectionTitle, { marginBottom: 8 }]}>Notes</Text>
            <Text>{notes}</Text>
          </View>
        )}

        {/* Footer */}
        <View style={styles.footer}>
          <Text>Thank you for your business!</Text>
        </View>
      </Page>
    </Document>
  );
}