"use client";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";
import { Download, Printer, Share2 } from "lucide-react";
import { getTemplateFontClass } from "./template-fonts";
// Import template configurations
const templateFonts = {
  modern: "font-sans",
  elegant: "font-lora",
  corporate: "font-sans",
  minimal: "font-sans",
  executive: "font-playfair",
  creative: "font-sans",
  professional: "font-dm-serif",
  premium: "font-cormorant"
} as const;

interface DocumentPreviewProps {
  type: "invoice" | "quote";
  documentData: {
    number: string;
    date: string;
    dueDate?: string;
    validUntil?: string;
    from: {
      name: string;
      address: string;
      email: string;
      phone: string;
    };
    to: {
      name: string;
      address: string;
      email: string;
      phone: string;
    };
    notes?: string;
    terms?: string;
  };
  items: Array<{
    description: string;
    quantity: number;
    rate: number;
    amount: number;
  }>;
  totals: {
    subtotal: number;
    tax: { rate: number; amount: number };
    discount: { rate: number; amount: number };
    total: number;
  };
  currency: string;
  template: string;
  onDownload?: () => void;
  className?: string;
}

export function DocumentPreview({
  type,
  documentData,
  items,
  totals,
  currency,
  template,
  onDownload,
  className,
}: DocumentPreviewProps) {
  const currencySymbol =
    currency === "USD"
      ? "$"
      : currency === "EUR"
      ? "€"
      : currency === "GBP"
      ? "£"
      : "$";
  const documentTitle = type === "invoice" ? "INVOICE" : "QUOTE";
  const dateLabel = type === "invoice" ? "Due Date" : "Valid Until";
  const dateValue =
    type === "invoice" ? documentData.dueDate : documentData.validUntil;
  const toLabel = type === "invoice" ? "Bill To" : "Quote For";

  // Template configurations
  const templateConfigs = {
    modern: {
      colors: "border-blue-200 bg-blue-50",
      font: "font-sans"
    },
    elegant: {
      colors: "border-slate-200 bg-slate-50",
      font: "font-lora"
    },
    corporate: {
      colors: "border-emerald-200 bg-emerald-50",
      font: "font-sans"
    },
    minimal: {
      colors: "border-gray-200 bg-gray-50",
      font: "font-sans"
    },
    executive: {
      colors: "border-amber-200 bg-amber-50",
      font: "font-playfair"
    },
    creative: {
      colors: "border-purple-200 bg-purple-50",
      font: "font-sans"
    },
    professional: {
      colors: "border-blue-200 bg-blue-50",
      font: "font-dm-serif"
    },
    premium: {
      colors: "border-indigo-200 bg-indigo-50",
      font: "font-cormorant"
    },
  };
  
  // Get the current template's configuration
  const templateConfig = {
    ...(templateConfigs[template as keyof typeof templateConfigs] || templateConfigs.modern),
    font: templateFonts[template as keyof typeof templateFonts] || 'font-sans'
  };

  const accentColors = {
    modern: "text-blue-600 border-blue-600",
    elegant: "text-slate-600 border-slate-600",
    corporate: "text-emerald-600 border-emerald-600",
    minimal: "text-gray-900 border-gray-900",
    executive: "text-amber-600 border-amber-600",
    creative: "text-purple-600 border-purple-600",
    professional: "text-blue-800 border-blue-800",
    premium: "text-indigo-700 border-indigo-700",
  } as const;
  
  // Get the current accent color
  const currentAccentColor = accentColors[template as keyof typeof accentColors] || accentColors.modern;

  return (
    <div className={cn("space-y-6", className)}>
      {/* Preview Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Document Preview</h3>
          <p className="text-sm text-muted-foreground">
            Live preview with {template} template
          </p>
        </div>
        <div className="flex gap-2">
          {/* <Button variant="outline" size="sm">
            <Printer className="h-4 w-4 mr-2" />
            Print
          </Button>
          <Button variant="outline" size="sm">
            <Share2 className="h-4 w-4 mr-2" />
            Share
          </Button> */}
          {onDownload && (
            <Button onClick={onDownload} size="sm">
              <Download className="h-4 w-4 mr-2" />
              Download
            </Button>
          )}
        </div>
      </div>

      {/* Document Preview */}
      <Card
        className={cn(
          "overflow-hidden shadow-lg",
          templateConfig.colors,
          templateConfig.font,
          "font-template-preview"
        )}
      >
        <CardContent className="p-0">
          {/* Document Paper */}
          <div className="bg-white mx-4 my-4 rounded-lg shadow-sm border">
            <div className="p-8 space-y-6">
              {/* Header */}
              <div className="flex justify-between items-start">
                <div className="space-y-2">
                  <h1
                    className={cn(
                      "text-3xl font-bold",
                      accentColors[template as keyof typeof accentColors] ||
                        accentColors.modern
                    )}
                  >
                    {documentTitle}
                  </h1>
                  <p className="text-lg text-gray-600">
                    #{documentData.number}
                  </p>
                  <Badge className="capitalize">{template} Template</Badge>
                </div>
                <div className="text-right space-y-1">
                  <p className="text-sm text-gray-500">Total Amount</p>
                  <p
                    className={cn(
                      "text-3xl font-bold",
                      accentColors[template as keyof typeof accentColors] ||
                        accentColors.modern
                    )}
                  >
                    {currencySymbol}
                    {totals.total.toFixed(2)}
                  </p>
                </div>
              </div>

              <Separator />

              {/* Business Information */}
              <div className="grid grid-cols-2 gap-8">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">From</h4>
                  <div className="space-y-1 text-sm">
                    <p className="font-medium text-gray-900">
                      {documentData.from.name || "Your Business Name"}
                    </p>
                    {documentData.from.address && (
                      <p className="text-gray-600 whitespace-pre-line">
                        {documentData.from.address}
                      </p>
                    )}
                    <p className="text-gray-600">
                      {documentData.from.email || "<EMAIL>"}
                    </p>
                    {documentData.from.phone && (
                      <p className="text-gray-600">{documentData.from.phone}</p>
                    )}
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">
                    {toLabel}
                  </h4>
                  <div className="space-y-1 text-sm">
                    <p className="font-medium text-gray-900">
                      {documentData.to.name || "Client Name"}
                    </p>
                    {documentData.to.address && (
                      <p className="text-gray-600 whitespace-pre-line">
                        {documentData.to.address}
                      </p>
                    )}
                    <p className="text-gray-600">
                      {documentData.to.email || "<EMAIL>"}
                    </p>
                    {documentData.to.phone && (
                      <p className="text-gray-600">{documentData.to.phone}</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Document Details */}
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-500">Issue Date:</span>
                  <span className="font-medium">{documentData.date}</span>
                </div>
                {dateValue && (
                  <div className="flex justify-between">
                    <span className="text-gray-500">{dateLabel}:</span>
                    <span className="font-medium">{dateValue}</span>
                  </div>
                )}
              </div>

              <Separator />

              {/* Items Table */}
              <div className="space-y-4">
                <h4 className="font-semibold text-gray-900">Items</h4>
                <div className="border rounded-lg overflow-hidden">
                  <div
                    className={cn(
                      "px-4 py-3 border-b font-medium text-sm",
                      currentAccentColor
                    )}
                  >
                    <div className="grid grid-cols-12 gap-4">
                      <div className="col-span-6 text-gray-900">
                        Description
                      </div>
                      <div className="col-span-2 text-center text-gray-900">
                        Qty
                      </div>
                      <div className="col-span-2 text-right text-gray-900">
                        Rate
                      </div>
                      <div className="col-span-2 text-right text-gray-900">
                        Amount
                      </div>
                    </div>
                  </div>
                  <div className="divide-y">
                    {items
                      .filter((item) => item.description)
                      .map((item, index) => (
                        <div key={index} className="px-4 py-3 text-sm">
                          <div className="grid grid-cols-12 gap-4">
                            <div className="col-span-6 text-gray-900">{item.description}</div>
                            <div className="col-span-2 text-center text-gray-700">
                              {item.quantity}
                            </div>
                            <div className="col-span-2 text-right text-gray-700">
                              {currencySymbol}
                              {item.rate.toFixed(2)}
                            </div>
                            <div className="col-span-2 text-right font-medium text-gray-900">
                              {currencySymbol}
                              {item.amount.toFixed(2)}
                            </div>
                          </div>
                        </div>
                      ))}
                    {items.filter((item) => item.description).length === 0 && (
                      <div className="px-4 py-8 text-center text-gray-500">
                        No items added yet
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Totals */}
              <div className="flex justify-end">
                <div className="w-80 space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Subtotal:</span>
                    <span>
                      {currencySymbol}
                      {totals.subtotal.toFixed(2)}
                    </span>
                  </div>
                  {totals.discount.amount > 0 && (
                    <div className="flex justify-between text-green-600">
                      <span>Discount ({totals.discount.rate}%):</span>
                      <span>
                        -{currencySymbol}
                        {totals.discount.amount.toFixed(2)}
                      </span>
                    </div>
                  )}
                  {totals.tax.amount > 0 && (
                    <div className="flex justify-between">
                      <span>Tax ({totals.tax.rate}%):</span>
                      <span>
                        {currencySymbol}
                        {totals.tax.amount.toFixed(2)}
                      </span>
                    </div>
                  )}
                  <Separator />
                  <div
                    className={cn(
                      "flex justify-between text-lg font-bold",
                      accentColors[template as keyof typeof accentColors] ||
                        accentColors.modern
                    )}
                  >
                    <span>Total:</span>
                    <span>
                      {currencySymbol}
                      {totals.total.toFixed(2)}
                    </span>
                  </div>
                </div>
              </div>

              {/* Terms and Notes */}
              {(documentData.terms || documentData.notes) && (
                <>
                  <Separator />
                  <div className="space-y-4">
                    {documentData.terms && (
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-2">
                          Terms & Conditions
                        </h4>
                        <p className="text-sm text-gray-600 whitespace-pre-line">
                          {documentData.terms}
                        </p>
                      </div>
                    )}
                    {documentData.notes && (
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-2">
                          Notes
                        </h4>
                        <p className="text-sm text-gray-600 whitespace-pre-line">
                          {documentData.notes}
                        </p>
                      </div>
                    )}
                  </div>
                </>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
