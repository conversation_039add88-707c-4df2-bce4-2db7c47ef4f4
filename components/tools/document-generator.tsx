'use client'

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import {
  DragEndEvent,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors
} from '@dnd-kit/core';
import {
  arrayMove,
  sortableKeyboardCoordinates,
  useSortable
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import {
  Building2,
  Calculator,
  Check,
  ChevronRight,
  FileText,
  GripVertical,
  Settings,
  Trash2,
  User
} from "lucide-react";
import React, { useCallback, useEffect, useState } from "react";
import { <PERSON>rate<PERSON><PERSON><PERSON>, ItemsSection, TemplateSection } from "./document-generator-sections";
import { TemplateType } from "./template-selector";

interface DocumentItem {
  id: string;
  description: string;
  quantity: number;
  rate: number;
  amount: number;
}

interface DocumentData {
  number: string;
  date: string;
  dueDate?: string;
  validUntil?: string;
  from: {
    name: string;
    address: string;
    email: string;
    phone: string;
    logo?: string;
  };
  to: {
    name: string;
    address: string;
    email: string;
    phone: string;
  };
  notes?: string;
  terms?: string;
}

interface DocumentGeneratorProps {
  type: "invoice" | "quote";
  title: string;
  description: string;
  primaryColor: string;
  icon: React.ComponentType<{ className?: string }>;
}

interface SideTabsProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  completedSections: Set<string>;
  type: "invoice" | "quote";
}

const SECTIONS = [
  { id: 'company', label: 'Company Info', icon: Building2 },
  { id: 'client', label: 'Client Info', icon: User },
  { id: 'details', label: 'Document Details', icon: FileText },
  { id: 'items', label: 'Items & Pricing', icon: Calculator },
  { id: 'template', label: 'Template & Notes', icon: Settings },
];

function SideTabs({ activeTab, onTabChange, completedSections, type }: SideTabsProps) {
  return (
    <div className="w-64 bg-gray-50 dark:bg-gray-900/50 border-r border-gray-200 dark:border-gray-800 p-4 flex flex-col">
      <div className="space-y-2">
        <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-4">
          {type === 'invoice' ? 'Invoice' : 'Quote'} Sections
        </h3>
        {SECTIONS.map((section, index) => {
          const Icon = section.icon;
          const isActive = activeTab === section.id;
          const isCompleted = completedSections.has(section.id);
          
          return (
            <button
              key={section.id}
              onClick={() => onTabChange(section.id)}
              className={cn(
                "w-full flex items-center gap-3 px-3 py-3 rounded-lg text-left transition-all duration-200",
                isActive
                  ? "bg-blue-600 text-white shadow-md"
                  : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
              )}
            >
              <div className={cn(
                "flex items-center justify-center w-8 h-8 rounded-full",
                isActive
                  ? "bg-white/20"
                  : isCompleted
                  ? "bg-green-100 dark:bg-green-900/30"
                  : "bg-gray-200 dark:bg-gray-700"
              )}>
                {isCompleted ? (
                  <Check className="w-4 h-4 text-green-600 dark:text-green-400" />
                ) : (
                  <Icon className={cn(
                    "w-4 h-4",
                    isActive ? "text-white" : "text-gray-500 dark:text-gray-400"
                  )} />
                )}
              </div>
              <div className="flex-1">
                <div className={cn(
                  "text-sm font-medium",
                  isActive ? "text-white" : "text-gray-900 dark:text-gray-100"
                )}>
                  {section.label}
                </div>
                <div className={cn(
                  "text-xs",
                  isActive ? "text-white/80" : "text-gray-500 dark:text-gray-400"
                )}>
                  Step {index + 1}
                </div>
              </div>
              {isActive && (
                <ChevronRight className="w-4 h-4 text-white" />
              )}
            </button>
          );
        })}
      </div>
      
      {/* Progress indicator */}
      <div className="mt-6 p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-2">
          <span className="text-xs font-medium text-gray-600 dark:text-gray-400">Progress</span>
          <span className="text-xs font-medium text-gray-900 dark:text-gray-100">
            {completedSections.size}/{SECTIONS.length}
          </span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div 
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${(completedSections.size / SECTIONS.length) * 100}%` }}
          />
        </div>
      </div>
    </div>
  );
}

function SortableItem({ item, index, updateItem, removeItem }: {
  item: DocumentItem;
  index: number;
  updateItem: (index: number, field: keyof DocumentItem, value: any) => void;
  removeItem: (index: number) => void;
}) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: item.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={cn(
        "grid gap-4 p-4 border rounded-lg bg-card transition-all duration-200",
        isDragging ? 'shadow-lg ring-2 ring-blue-500/20' : 'hover:shadow-md'
      )}
    >
      <div className="flex items-center gap-3">
        <div
          {...attributes}
          {...listeners}
          className="cursor-grab active:cursor-grabbing p-2 hover:bg-muted rounded-md transition-colors"
        >
          <GripVertical className="h-4 w-4 text-muted-foreground" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 flex-1">
          <div className="md:col-span-2 space-y-2">
            <Label className="text-sm font-medium">Description</Label>
            <Input
              value={item.description}
              onChange={(e) => updateItem(index, "description", e.target.value)}
              placeholder="Enter item description..."
              className="h-10"
            />
          </div>
          <div className="space-y-2">
            <Label className="text-sm font-medium">Quantity</Label>
            <Input
              type="number"
              min="1"
              value={item.quantity}
              onChange={(e) => updateItem(index, "quantity", parseInt(e.target.value) || 1)}
              className="h-10"
            />
          </div>
          <div className="space-y-2">
            <Label className="text-sm font-medium">Rate</Label>
            <Input
              type="number"
              min="0"
              step="0.01"
              value={item.rate}
              onChange={(e) => updateItem(index, "rate", parseFloat(e.target.value) || 0)}
              className="h-10"
            />
          </div>
        </div>
        <div className="flex flex-col items-end gap-2">
          <div className="text-sm font-semibold text-gray-900 dark:text-gray-100">
            ${item.amount.toFixed(2)}
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => removeItem(index)}
            className="text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}

export function DocumentGenerator({ type, title, description, primaryColor, icon: Icon }: DocumentGeneratorProps) {
  const [activeTab, setActiveTab] = useState('company');
  const [completedSections, setCompletedSections] = useState<Set<string>>(new Set());
  const [items, setItems] = useState<DocumentItem[]>([
    { id: "1", description: "Item 1", quantity: 1, rate: 10, amount: 10 },
  ]);
  const [selectedTemplate, setSelectedTemplate] = useState<TemplateType>("modern");
  const [currency, setCurrency] = useState("USD");
  const [taxRate, setTaxRate] = useState(0);
  const [discountRate, setDiscountRate] = useState(0);
  const [showPreview, setShowPreview] = useState(false);
  
  const [documentData, setDocumentData] = useState<DocumentData>({
    number: `${type.toUpperCase().slice(0, 3)}-${Date.now().toString().slice(-6)}`,
    date: new Date().toISOString().split("T")[0],
    dueDate: type === 'invoice' ? "" : undefined,
    validUntil: type === 'quote' ? "" : undefined,
    from: {
      name: "John Doe",
      address: "123 Main St, Anytown, CA 12345",
      email: "<EMAIL>",
      phone: "************",
      logo: "",
    },
    to: {
      name: "Jane Doe",
      address: "456 Elm St, Othertown, NY 67890",
      email: "<EMAIL>",
      phone: "************",
    },
    notes: "This is a test note.",
    terms: type === 'quote' ? "" : undefined,
  });

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Check if a section is completed
  const checkSectionCompletion = (sectionId: string): boolean => {
    switch (sectionId) {
      case 'company':
        return !!(documentData.from.name && documentData.from.email && documentData.from.address);
      case 'client':
        return !!(documentData.to.name && documentData.to.email && documentData.to.address);
      case 'details':
        return !!(documentData.number && documentData.date);
      case 'items':
        return items.some(item => item.description && item.quantity > 0 && item.rate > 0);
      case 'template':
        return true; // Template section is always considered complete
      default:
        return false;
    }
  };

  // Update completed sections when data changes
  const updateCompletedSections = useCallback(() => {
    const newCompleted = new Set<string>();
    SECTIONS.forEach(section => {
      if (checkSectionCompletion(section.id)) {
        newCompleted.add(section.id);
      }
    });
    setCompletedSections(newCompleted);
  }, [documentData, items]);

  // Call updateCompletedSections whenever relevant data changes
  useEffect(() => {
    updateCompletedSections();
  }, [updateCompletedSections]);

  // Helper functions for calculations
  const addItem = () => {
    const newId = Date.now().toString();
    setItems([...items, { id: newId, description: "", quantity: 1, rate: 0, amount: 0 }]);
  };

  const removeItem = (index: number) => {
    if (items.length > 1) {
      setItems(items.filter((_, i) => i !== index));
    }
    updateCompletedSections();
  };

  const updateItem = (index: number, field: keyof DocumentItem, value: any) => {
    const newItems = [...items];
    newItems[index] = { ...newItems[index], [field]: value };

    // Calculate amount when quantity or rate changes
    if (field === "quantity" || field === "rate") {
      newItems[index].amount = newItems[index].quantity * newItems[index].rate;
    }

    setItems(newItems);
    updateCompletedSections();
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const oldIndex = items.findIndex((item) => item.id === active.id);
      const newIndex = items.findIndex((item) => item.id === over?.id);

      setItems(arrayMove(items, oldIndex, newIndex));
    }
  };

  const calculateSubtotal = () => {
    return items.reduce((sum, item) => sum + item.amount, 0);
  };

  const calculateTax = () => {
    const subtotal = calculateSubtotal();
    const amount = (subtotal * taxRate) / 100;
    return { rate: taxRate, amount };
  };

  const calculateDiscount = () => {
    const subtotal = calculateSubtotal();
    const amount = (subtotal * discountRate) / 100;
    return { rate: discountRate, amount };
  };

  const calculateTotal = () => {
    const subtotal = calculateSubtotal();
    const tax = calculateTax();
    const discount = calculateDiscount();
    return subtotal + tax.amount - discount.amount;
  };

  const isFormValid = (): boolean => {
    return !!(
      documentData.from.name &&
      documentData.from.email &&
      documentData.from.address &&
      documentData.to.name &&
      documentData.to.email &&
      documentData.to.address &&
      documentData.number &&
      documentData.date &&
      items.some(item => item.description && item.quantity > 0 && item.rate > 0)
    );
  };

  return (
    <div className="flex flex-col bg-gray-50 dark:bg-gray-900 h-screen">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <div className="flex items-center gap-4">
          <div className={cn("w-12 h-12 rounded-lg flex items-center justify-center", primaryColor)}>
            <Icon className="h-6 w-6 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">{title}</h1>
            <p className="text-gray-600 dark:text-gray-400">{description}</p>
          </div>
        </div>
      </div>

      <div className="flex h-screen overflow-hidden">
        {/* Side Navigation */}
        <SideTabs 
          activeTab={activeTab} 
          onTabChange={setActiveTab} 
          completedSections={completedSections}
          type={type}
        />

        {/* Main Content */}
        <div className="flex-1 p-6 h-[calc(100vh-185px)] overflow-y-auto">
          <div className="max-w-4xl mx-auto">
            {activeTab === 'company' && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Building2 className="h-5 w-5" />
                    Your Company Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="companyName">Company Name *</Label>
                      <Input
                        id="companyName"
                        value={documentData.from.name}
                        onChange={(e) => {
                          setDocumentData({ ...documentData, from: { ...documentData.from, name: e.target.value } });
                          updateCompletedSections();
                        }}
                        placeholder="Your Company Name"
                        className="h-11"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="companyEmail">Email Address *</Label>
                      <Input
                        id="companyEmail"
                        type="email"
                        value={documentData.from.email}
                        onChange={(e) => {
                          setDocumentData({ ...documentData, from: { ...documentData.from, email: e.target.value } });
                          updateCompletedSections();
                        }}
                        placeholder="<EMAIL>"
                        className="h-11"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="companyAddress">Company Address *</Label>
                    <Textarea
                      id="companyAddress"
                      value={documentData.from.address}
                      onChange={(e) => {
                        setDocumentData({ ...documentData, from: { ...documentData.from, address: e.target.value } });
                        updateCompletedSections();
                      }}
                      placeholder="123 Business Street, City, State, ZIP"
                      rows={3}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="companyPhone">Phone Number</Label>
                    <Input
                      id="companyPhone"
                      value={documentData.from.phone}
                      onChange={(e) => {
                        setDocumentData({ ...documentData, from: { ...documentData.from, phone: e.target.value } });
                        updateCompletedSections();
                      }}
                      placeholder="+****************"
                      className="h-11"
                    />
                  </div>
                </CardContent>
              </Card>
            )}

            {activeTab === 'client' && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    Client Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="clientName">Client Name *</Label>
                      <Input
                        id="clientName"
                        value={documentData.to.name}
                        onChange={(e) => {
                          setDocumentData({ ...documentData, to: { ...documentData.to, name: e.target.value } });
                          updateCompletedSections();
                        }}
                        placeholder="Client Company or Name"
                        className="h-11"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="clientEmail">Client Email *</Label>
                      <Input
                        id="clientEmail"
                        type="email"
                        value={documentData.to.email}
                        onChange={(e) => {
                          setDocumentData({ ...documentData, to: { ...documentData.to, email: e.target.value } });
                          updateCompletedSections();
                        }}
                        placeholder="<EMAIL>"
                        className="h-11"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="clientAddress">Client Address *</Label>
                    <Textarea
                      id="clientAddress"
                      value={documentData.to.address}
                      onChange={(e) => {
                        setDocumentData({ ...documentData, to: { ...documentData.to, address: e.target.value } });
                        updateCompletedSections();
                      }}
                      placeholder="123 Client Street, City, State, ZIP"
                      rows={3}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="clientPhone">Client Phone</Label>
                    <Input
                      id="clientPhone"
                      value={documentData.to.phone}
                      onChange={(e) => {
                        setDocumentData({ ...documentData, to: { ...documentData.to, phone: e.target.value } });
                        updateCompletedSections();
                      }}
                      placeholder="+****************"
                      className="h-11"
                    />
                  </div>
                </CardContent>
              </Card>
            )}

            {activeTab === 'details' && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    {type === 'invoice' ? 'Invoice' : 'Quote'} Details
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="documentNumber">{type === 'invoice' ? 'Invoice' : 'Quote'} Number *</Label>
                      <Input
                        id="documentNumber"
                        value={documentData.number}
                        onChange={(e) => {
                          setDocumentData({ ...documentData, number: e.target.value });
                          updateCompletedSections();
                        }}
                        placeholder={type === 'invoice' ? 'INV-001' : 'QUO-001'}
                        className="h-11"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="date">Issue Date *</Label>
                      <Input
                        id="date"
                        type="date"
                        value={documentData.date}
                        onChange={(e) => {
                          setDocumentData({ ...documentData, date: e.target.value });
                          updateCompletedSections();
                        }}
                        className="h-11"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="dueDate">
                        {type === 'invoice' ? 'Due Date' : 'Valid Until'}
                      </Label>
                      <Input
                        id="dueDate"
                        type="date"
                        value={type === 'invoice' ? documentData.dueDate : documentData.validUntil}
                        onChange={(e) => {
                          if (type === 'invoice') {
                            setDocumentData({ ...documentData, dueDate: e.target.value });
                          } else {
                            setDocumentData({ ...documentData, validUntil: e.target.value });
                          }
                          updateCompletedSections();
                        }}
                        className="h-11"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="currency">Currency</Label>
                      <Select value={currency} onValueChange={setCurrency}>
                        <SelectTrigger className="h-11">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="USD">USD ($)</SelectItem>
                          <SelectItem value="EUR">EUR (€)</SelectItem>
                          <SelectItem value="GBP">GBP (£)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="taxRate">Tax Rate (%)</Label>
                      <Input
                        id="taxRate"
                        type="number"
                        min="0"
                        max="100"
                        step="0.01"
                        value={taxRate}
                        onChange={(e) => setTaxRate(parseFloat(e.target.value) || 0)}
                        placeholder="0.00"
                        className="h-11"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="discountRate">Discount Rate (%)</Label>
                      <Input
                        id="discountRate"
                        type="number"
                        min="0"
                        max="100"
                        step="0.01"
                        value={discountRate}
                        onChange={(e) => setDiscountRate(parseFloat(e.target.value) || 0)}
                        placeholder="0.00"
                        className="h-11"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {activeTab === 'items' && (
              <ItemsSection
                items={items}
                setItems={setItems}
                currency={currency}
                taxRate={taxRate}
                discountRate={discountRate}
                updateCompletedSections={updateCompletedSections}
              />
            )}

            {activeTab === 'template' && (
              <TemplateSection
                type={type}
                selectedTemplate={selectedTemplate}
                setSelectedTemplate={setSelectedTemplate}
                documentData={documentData}
                setDocumentData={setDocumentData}
              />
            )}
          </div>
        </div>

        {/* Generate Button */}
        <GenerateButton
          type={type}
          documentData={documentData}
          items={items}
          selectedTemplate={selectedTemplate}
          currency={currency}
          calculateSubtotal={calculateSubtotal}
          calculateTax={calculateTax}
          calculateDiscount={calculateDiscount}
          calculateTotal={calculateTotal}
          isFormValid={isFormValid}
          setShowPreview={setShowPreview}
          primaryColor={primaryColor}
        />
      </div>
    </div>
  );
}
