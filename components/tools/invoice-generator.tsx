'use client'
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import {
  closestCenter,
  DndContext,
  DragEndEvent,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
const PDFDownloadLink = dynamic(
  () => import('@react-pdf/renderer').then(mod => mod.PDFDownloadLink),
  { ssr: false }
);
import {
  Building2,
  Calculator,
  Download,
  Eye,
  FileText,
  GripVertical,
  Plus,
  Settings,
  Trash2,
  User
} from "lucide-react";
import { useState } from "react";
import { PDFTemplate } from "./pdf-template";
import { TemplateSelector, TemplateType } from "./template-selector";
import dynamic from "next/dynamic";

interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  rate: number;
  amount: number;
}

interface SortableItemProps {
  item: InvoiceItem;
  index: number;
  updateItem: (index: number, field: keyof InvoiceItem, value: any) => void;
  removeItem: (index: number) => void;
}

function SortableItem({ item, index, updateItem, removeItem }: SortableItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: item.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`grid gap-4 p-4 border rounded-lg bg-card ${isDragging ? 'shadow-lg' : ''}`}
    >
      <div className="flex items-center gap-2">
        <div
          {...attributes}
          {...listeners}
          className="cursor-grab active:cursor-grabbing p-1 hover:bg-muted rounded"
        >
          <GripVertical className="h-4 w-4 text-muted-foreground" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 flex-1">
          <div className="md:col-span-2 space-y-2">
            <Label>Description</Label>
            <Input
              value={item.description}
              onChange={(e) => updateItem(index, "description", e.target.value)}
              placeholder="Item description..."
            />
          </div>
          <div className="space-y-2">
            <Label>Quantity</Label>
            <Input
              type="number"
              min="1"
              value={item.quantity}
              onChange={(e) => updateItem(index, "quantity", parseInt(e.target.value) || 1)}
            />
          </div>
          <div className="space-y-2">
            <Label>Rate ($)</Label>
            <Input
              type="number"
              min="0"
              step="0.01"
              value={item.rate}
              onChange={(e) => updateItem(index, "rate", parseFloat(e.target.value) || 0)}
            />
          </div>
        </div>
        <div className="flex flex-col items-end gap-2">
          <div className="text-sm font-medium">
            ${item.amount.toFixed(2)}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => removeItem(index)}
            className="text-destructive hover:text-destructive"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}

export function InvoiceGenerator() {
  const [items, setItems] = useState<InvoiceItem[]>([
    { id: "1", description: "", quantity: 1, rate: 0, amount: 0 },
  ]);
  const [selectedTemplate, setSelectedTemplate] = useState<TemplateType>("modern");
  const [currency, setCurrency] = useState("USD");
  const [taxRate, setTaxRate] = useState(0);
  const [discountRate, setDiscountRate] = useState(0);
  const [showPreview, setShowPreview] = useState(false);
  const [invoiceData, setInvoiceData] = useState({
    invoiceNumber: `INV-${Date.now().toString().slice(-6)}`,
    date: new Date().toISOString().split("T")[0],
    dueDate: "",
    from: {
      name: "",
      address: "",
      email: "",
      phone: "",
      logo: "",
    },
    to: {
      name: "",
      address: "",
      email: "",
      phone: "",
    },
    notes: "",
  });

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const addItem = () => {
    const newId = Date.now().toString();
    setItems([...items, { id: newId, description: "", quantity: 1, rate: 0, amount: 0 }]);
  };

  const removeItem = (index: number) => {
    if (items.length > 1) {
      setItems(items.filter((_, i) => i !== index));
    }
  };

  const updateItem = (index: number, field: keyof InvoiceItem, value: any) => {
    const newItems = [...items];
    newItems[index] = { ...newItems[index], [field]: value };

    // Calculate amount when quantity or rate changes
    if (field === "quantity" || field === "rate") {
      newItems[index].amount = newItems[index].quantity * newItems[index].rate;
    }

    setItems(newItems);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      setItems((items) => {
        const oldIndex = items.findIndex((item) => item.id === active.id);
        const newIndex = items.findIndex((item) => item.id === over?.id);

        return arrayMove(items, oldIndex, newIndex);
      });
    }
  };

  const calculateSubtotal = () => {
    return items.reduce((sum, item) => sum + item.amount, 0);
  };

  const calculateDiscount = () => {
    const subtotal = calculateSubtotal();
    return {
      rate: discountRate,
      amount: (subtotal * discountRate) / 100,
    };
  };

  const calculateTax = () => {
    const subtotal = calculateSubtotal();
    const discount = calculateDiscount();
    const taxableAmount = subtotal - discount.amount;
    return {
      rate: taxRate,
      amount: (taxableAmount * taxRate) / 100,
    };
  };

  const calculateTotal = () => {
    const subtotal = calculateSubtotal();
    const discount = calculateDiscount();
    const tax = calculateTax();
    return subtotal - discount.amount + tax.amount;
  };

  const isFormValid = () => {
    return (
      invoiceData.invoiceNumber &&
      invoiceData.from.name &&
      invoiceData.from.email &&
      invoiceData.to.name &&
      invoiceData.to.email &&
      items.some(item => item.description && item.quantity > 0 && item.rate > 0)
    );
  };

  return (
    <div className="space-y-8">
      {/* Professional Header */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 rounded-xl p-8 border border-blue-100 dark:border-blue-900/20">
        <div className="flex flex-col lg:flex-row gap-6 items-start lg:items-center justify-between">
          <div className="space-y-2">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                <FileText className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Invoice Generator</h1>
                <p className="text-blue-600 dark:text-blue-400 font-medium">Professional • Fast • Reliable</p>
              </div>
            </div>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl">
              Create professional invoices in minutes with our advanced template system.
              Choose from premium designs and customize every detail to match your brand.
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-3">
            <Sheet open={showPreview} onOpenChange={setShowPreview}>
              <SheetTrigger asChild>
                <Button variant="outline" size="lg" className="bg-white/50 backdrop-blur-sm">
                  <Eye className="mr-2 h-4 w-4" />
                  Live Preview
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-full sm:max-w-3xl">
                <SheetHeader>
                  <SheetTitle className="text-xl">Invoice Preview</SheetTitle>
                  <SheetDescription>
                    Real-time preview of your invoice with the selected template
                  </SheetDescription>
                </SheetHeader>
                <div className="mt-6 space-y-6">
                  {/* Enhanced Preview Content */}
                  <div className="bg-white rounded-lg border shadow-sm p-6">
                    <div className="flex justify-between items-start mb-6">
                      <div>
                        <h3 className="text-2xl font-bold text-gray-900">INVOICE</h3>
                        <p className="text-lg text-gray-600">#{invoiceData.invoiceNumber}</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-gray-500">Total Amount</p>
                        <p className="text-3xl font-bold text-blue-600">${calculateTotal().toFixed(2)}</p>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-6 mb-6">
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-2">From</h4>
                        <div className="text-sm text-gray-600 space-y-1">
                          <p className="font-medium">{invoiceData.from.name || "Your Business Name"}</p>
                          <p>{invoiceData.from.email || "<EMAIL>"}</p>
                          <p>{invoiceData.from.phone || "+****************"}</p>
                        </div>
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-2">Bill To</h4>
                        <div className="text-sm text-gray-600 space-y-1">
                          <p className="font-medium">{invoiceData.to.name || "Client Name"}</p>
                          <p>{invoiceData.to.email || "<EMAIL>"}</p>
                          <p>{invoiceData.to.phone || "+****************"}</p>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2 mb-4">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-500">Issue Date:</span>
                        <span>{invoiceData.date}</span>
                      </div>
                      {invoiceData.dueDate && (
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-500">Due Date:</span>
                          <span>{invoiceData.dueDate}</span>
                        </div>
                      )}
                    </div>

                    {/* Items Preview */}
                    <div className="border rounded-lg overflow-hidden">
                      <div className="bg-gray-50 px-4 py-2 border-b">
                        <div className="grid grid-cols-4 gap-4 text-sm font-medium text-gray-700">
                          <span>Description</span>
                          <span>Qty</span>
                          <span>Rate</span>
                          <span className="text-right">Amount</span>
                        </div>
                      </div>
                      <div className="divide-y">
                        {items.slice(0, 3).map((item, index) => (
                          <div key={index} className="px-4 py-3">
                            <div className="grid grid-cols-4 gap-4 text-sm">
                              <span>{item.description || `Item ${index + 1}`}</span>
                              <span>{item.quantity}</span>
                              <span>${item.rate.toFixed(2)}</span>
                              <span className="text-right">${item.amount.toFixed(2)}</span>
                            </div>
                          </div>
                        ))}
                        {items.length > 3 && (
                          <div className="px-4 py-3 text-center text-sm text-gray-500">
                            ... and {items.length - 3} more items
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </SheetContent>
            </Sheet>

            {isFormValid() && (
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
                <Download className="mr-2 h-4 w-4" />
                Generate PDF
              </Button>
            )}
          </div>
        </div>

        {/* Progress Indicator */}
        <div className="mt-6 pt-6 border-t border-blue-200 dark:border-blue-800">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600 dark:text-gray-300">Progress</span>
            <span className="text-blue-600 dark:text-blue-400 font-medium">
              {isFormValid() ? "Ready to generate" : "Fill required fields"}
            </span>
          </div>
          <div className="mt-2 w-full bg-blue-100 dark:bg-blue-900/30 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{
                width: `${isFormValid() ? 100 :
                  ((invoiceData.from.name ? 25 : 0) +
                   (invoiceData.from.email ? 25 : 0) +
                   (invoiceData.to.name ? 25 : 0) +
                   (invoiceData.to.email ? 25 : 0))}%`
              }}
            />
          </div>
        </div>
      </div>

      <Tabs defaultValue="details" className="w-full">
        <TabsList className="grid w-full grid-cols-4 h-14 bg-gray-100 dark:bg-gray-800 p-1 rounded-xl">
          <TabsTrigger
            value="details"
            className="flex items-center gap-2 h-12 rounded-lg data-[state=active]:bg-white data-[state=active]:shadow-sm dark:data-[state=active]:bg-gray-700 transition-all duration-200"
          >
            <FileText className="h-4 w-4" />
            <span className="hidden sm:inline">Invoice Details</span>
            <span className="sm:hidden">Details</span>
          </TabsTrigger>
          <TabsTrigger
            value="parties"
            className="flex items-center gap-2 h-12 rounded-lg data-[state=active]:bg-white data-[state=active]:shadow-sm dark:data-[state=active]:bg-gray-700 transition-all duration-200"
          >
            <User className="h-4 w-4" />
            <span className="hidden sm:inline">Business Info</span>
            <span className="sm:hidden">Parties</span>
          </TabsTrigger>
          <TabsTrigger
            value="items"
            className="flex items-center gap-2 h-12 rounded-lg data-[state=active]:bg-white data-[state=active]:shadow-sm dark:data-[state=active]:bg-gray-700 transition-all duration-200"
          >
            <Calculator className="h-4 w-4" />
            <span className="hidden sm:inline">Line Items</span>
            <span className="sm:hidden">Items</span>
          </TabsTrigger>
          <TabsTrigger
            value="template"
            className="flex items-center gap-2 h-12 rounded-lg data-[state=active]:bg-white data-[state=active]:shadow-sm dark:data-[state=active]:bg-gray-700 transition-all duration-200"
          >
            <Settings className="h-4 w-4" />
            <span className="hidden sm:inline">Design & Notes</span>
            <span className="sm:hidden">Template</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Invoice Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="invoiceNumber">Invoice Number</Label>
                  <Input
                    id="invoiceNumber"
                    value={invoiceData.invoiceNumber}
                    onChange={(e) =>
                      setInvoiceData({ ...invoiceData, invoiceNumber: e.target.value })
                    }
                    placeholder="INV-001"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="date">Issue Date</Label>
                  <Input
                    id="date"
                    type="date"
                    value={invoiceData.date}
                    onChange={(e) =>
                      setInvoiceData({ ...invoiceData, date: e.target.value })
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="dueDate">Due Date</Label>
                  <Input
                    id="dueDate"
                    type="date"
                    value={invoiceData.dueDate}
                    onChange={(e) =>
                      setInvoiceData({ ...invoiceData, dueDate: e.target.value })
                    }
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="currency">Currency</Label>
                  <Select value={currency} onValueChange={setCurrency}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="USD">USD ($)</SelectItem>
                      <SelectItem value="EUR">EUR (€)</SelectItem>
                      <SelectItem value="GBP">GBP (£)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="taxRate">Tax Rate (%)</Label>
                  <Input
                    id="taxRate"
                    type="number"
                    min="0"
                    max="100"
                    step="0.1"
                    value={taxRate}
                    onChange={(e) => setTaxRate(parseFloat(e.target.value) || 0)}
                    placeholder="0"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="discountRate">Discount (%)</Label>
                  <Input
                    id="discountRate"
                    type="number"
                    min="0"
                    max="100"
                    step="0.1"
                    value={discountRate}
                    onChange={(e) => setDiscountRate(parseFloat(e.target.value) || 0)}
                    placeholder="0"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="parties" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building2 className="h-5 w-5" />
                  From (Your Business)
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="fromName">Business Name *</Label>
                  <Input
                    id="fromName"
                    value={invoiceData.from.name}
                    onChange={(e) =>
                      setInvoiceData({
                        ...invoiceData,
                        from: { ...invoiceData.from, name: e.target.value },
                      })
                    }
                    placeholder="Your Business Name"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="fromAddress">Address</Label>
                  <Textarea
                    id="fromAddress"
                    value={invoiceData.from.address}
                    onChange={(e) =>
                      setInvoiceData({
                        ...invoiceData,
                        from: { ...invoiceData.from, address: e.target.value },
                      })
                    }
                    placeholder="123 Business St&#10;City, State 12345&#10;Country"
                    rows={3}
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="fromEmail">Email *</Label>
                    <Input
                      id="fromEmail"
                      type="email"
                      value={invoiceData.from.email}
                      onChange={(e) =>
                        setInvoiceData({
                          ...invoiceData,
                          from: { ...invoiceData.from, email: e.target.value },
                        })
                      }
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="fromPhone">Phone</Label>
                    <Input
                      id="fromPhone"
                      type="tel"
                      value={invoiceData.from.phone}
                      onChange={(e) =>
                        setInvoiceData({
                          ...invoiceData,
                          from: { ...invoiceData.from, phone: e.target.value },
                        })
                      }
                      placeholder="+****************"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Bill To (Client)
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="toName">Client Name *</Label>
                  <Input
                    id="toName"
                    value={invoiceData.to.name}
                    onChange={(e) =>
                      setInvoiceData({
                        ...invoiceData,
                        to: { ...invoiceData.to, name: e.target.value },
                      })
                    }
                    placeholder="Client Name or Company"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="toAddress">Address</Label>
                  <Textarea
                    id="toAddress"
                    value={invoiceData.to.address}
                    onChange={(e) =>
                      setInvoiceData({
                        ...invoiceData,
                        to: { ...invoiceData.to, address: e.target.value },
                      })
                    }
                    placeholder="456 Client Ave&#10;City, State 67890&#10;Country"
                    rows={3}
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="toEmail">Email *</Label>
                    <Input
                      id="toEmail"
                      type="email"
                      value={invoiceData.to.email}
                      onChange={(e) =>
                        setInvoiceData({
                          ...invoiceData,
                          to: { ...invoiceData.to, email: e.target.value },
                        })
                      }
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="toPhone">Phone</Label>
                    <Input
                      id="toPhone"
                      type="tel"
                      value={invoiceData.to.phone}
                      onChange={(e) =>
                        setInvoiceData({
                          ...invoiceData,
                          to: { ...invoiceData.to, phone: e.target.value },
                        })
                      }
                      placeholder="+****************"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="items" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Calculator className="h-5 w-5" />
                  Invoice Items
                </CardTitle>
                <Button variant="outline" size="sm" onClick={addItem}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Item
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <DndContext
                sensors={sensors}
                collisionDetection={closestCenter}
                onDragEnd={handleDragEnd}
              >
                <SortableContext items={items.map(item => item.id)} strategy={verticalListSortingStrategy}>
                  <div className="space-y-4">
                    {items.map((item, index) => (
                      <SortableItem
                        key={item.id}
                        item={item}
                        index={index}
                        updateItem={updateItem}
                        removeItem={removeItem}
                      />
                    ))}
                  </div>
                </SortableContext>
              </DndContext>

              {/* Totals Summary */}
              <div className="mt-8 space-y-4">
                <Separator />
                <div className="flex justify-end">
                  <div className="w-full max-w-sm space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Subtotal:</span>
                      <span>${calculateSubtotal().toFixed(2)}</span>
                    </div>
                    {discountRate > 0 && (
                      <div className="flex justify-between text-sm text-green-600">
                        <span>Discount ({discountRate}%):</span>
                        <span>-${calculateDiscount().amount.toFixed(2)}</span>
                      </div>
                    )}
                    {taxRate > 0 && (
                      <div className="flex justify-between text-sm">
                        <span>Tax ({taxRate}%):</span>
                        <span>${calculateTax().amount.toFixed(2)}</span>
                      </div>
                    )}
                    <Separator />
                    <div className="flex justify-between font-bold text-lg">
                      <span>Total:</span>
                      <span>${calculateTotal().toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="template" className="space-y-6">
          <TemplateSelector
            selectedTemplate={selectedTemplate}
            onTemplateChange={setSelectedTemplate}
          />

          <Card>
            <CardHeader>
              <CardTitle>Additional Notes</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Label htmlFor="notes">Notes (Optional)</Label>
                <Textarea
                  id="notes"
                  value={invoiceData.notes}
                  onChange={(e) =>
                    setInvoiceData({ ...invoiceData, notes: e.target.value })
                  }
                  placeholder="Payment terms, additional information, etc."
                  rows={4}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Professional Generate Section */}
      <div className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 rounded-xl p-8 border">
        <div className="text-center space-y-6">
          <div className="space-y-2">
            <h3 className="text-xl font-semibold">Ready to Generate Your Invoice?</h3>
            <p className="text-muted-foreground">
              Your professional invoice will be generated as a PDF with your selected template design.
            </p>
          </div>

          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl mx-auto">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border">
              <div className="text-sm text-muted-foreground">Invoice Total</div>
              <div className="text-2xl font-bold text-blue-600">${calculateTotal().toFixed(2)}</div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border">
              <div className="text-sm text-muted-foreground">Items Count</div>
              <div className="text-2xl font-bold">{items.filter(item => item.description).length}</div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border">
              <div className="text-sm text-muted-foreground">Template</div>
              <div className="text-lg font-semibold capitalize">{selectedTemplate}</div>
            </div>
          </div>

          {/* Generate Button */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            {isFormValid() ? (
              <PDFDownloadLink
                document={
                  <PDFTemplate
                    type="invoice"
                    template={selectedTemplate}
                    number={invoiceData.invoiceNumber}
                    date={invoiceData.date}
                    dueDate={invoiceData.dueDate}
                    from={invoiceData.from}
                    to={invoiceData.to}
                    items={items}
                    subtotal={calculateSubtotal()}
                    tax={calculateTax()}
                    discount={calculateDiscount()}
                    total={calculateTotal()}
                    currency={currency}
                    notes={invoiceData.notes}
                  />
                }
                fileName={`invoice-${invoiceData.invoiceNumber}.pdf`}
              >
                {({ loading }) => (
                  <Button
                    size="lg"
                    disabled={loading}
                    className="min-w-[250px] h-12 bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl transition-all duration-200"
                  >
                    <Download className="mr-2 h-5 w-5" />
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Generating PDF...
                      </>
                    ) : (
                      "Generate Professional Invoice"
                    )}
                  </Button>
                )}
              </PDFDownloadLink>
            ) : (
              <Button size="lg" disabled className="min-w-[250px] h-12 cursor-pointer">
                <Download className="mr-2 h-5 w-5" />
                Complete Required Fields
              </Button>
            )}

            <Button variant="outline" size="lg" onClick={() =>{
              setShowPreview(true);
              console.log("show preview");
            }} className="cursor-pointer">
              <Eye className="mr-2 h-4 w-4" />
              Preview First
            </Button>
          </div>

          {/* Status Indicator */}
          <div className="flex justify-center">
            <div className={cn(
              "flex items-center gap-3 px-4 py-2 rounded-full text-sm font-medium",
              isFormValid()
                ? "bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400"
                : "bg-amber-100 text-amber-700 dark:bg-amber-900/20 dark:text-amber-400"
            )}>
              <div className={cn(
                "w-2 h-2 rounded-full",
                isFormValid() ? "bg-green-500" : "bg-amber-500"
              )} />
              {isFormValid() ? (
                <span>✓ Ready to generate your professional invoice</span>
              ) : (
                <span>⚠ Please complete all required fields to continue</span>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}