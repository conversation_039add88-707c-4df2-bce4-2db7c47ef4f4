"use client";

import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Github, MenuIcon, SearchIcon, GlobeIcon } from "lucide-react";
import { useState } from "react";
import { MotionEffect } from "@/components/animate-ui/effects/motion-effect";
import { CopyButton } from "@/components/animate-ui/buttons/copy";

export function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center">
        <div className="mr-4 flex">
          <Link href="/" className="mr-6 flex items-center space-x-2 group">
            <GlobeIcon className="h-6 w-6 text-primary group-hover:rotate-12 transition-transform duration-300" />
            <span className="font-bold text-lg group-hover:text-primary transition-colors">
              All-in-One Tools
            </span>
          </Link>
        </div>

        {/* Desktop Navigation */}
        <div className="hidden md:flex flex-1 items-center justify-between space-x-2">
          <nav className="flex items-center space-x-1">
            <Button variant="ghost" size="sm" asChild>
              <Link href="#tools" className="flex items-center gap-2">
                <SearchIcon className="h-4 w-4" />
                Browse Tools
              </Link>
            </Button>
            <Button variant="ghost" size="sm" asChild>
              <Link href="/business/invoice-generator" className="flex items-center gap-2">
                Invoice Generator
                <Badge variant="secondary" className="text-xs">Popular</Badge>
              </Link>
            </Button>
          </nav>

          <div className="flex items-center space-x-2">
            <CopyButton
              content="https://allinonetools.dev"
              variant="ghost"
              size="sm"
              className="hidden lg:flex"
            />
            <Button variant="ghost" size="sm" asChild>
              <Link href="https://github.com/yourusername/allinonetools" target="_blank" rel="noreferrer">
                <Github className="h-4 w-4" />
                <span className="sr-only">GitHub</span>
              </Link>
            </Button>
          </div>
        </div>

        {/* Mobile Menu Button */}
        <div className="flex md:hidden flex-1 justify-end">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <MenuIcon className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <MotionEffect
          fade
          slide={{ direction: "down", offset: 20 }}
          className="md:hidden border-t bg-background/95 backdrop-blur"
        >
          <div className="container py-4 space-y-2">
            <Button variant="ghost" size="sm" asChild className="w-full justify-start">
              <Link href="#tools" onClick={() => setIsMenuOpen(false)}>
                <SearchIcon className="h-4 w-4 mr-2" />
                Browse Tools
              </Link>
            </Button>
            <Button variant="ghost" size="sm" asChild className="w-full justify-start">
              <Link href="/business/invoice-generator" onClick={() => setIsMenuOpen(false)}>
                Invoice Generator
              </Link>
            </Button>
            <Button variant="ghost" size="sm" asChild className="w-full justify-start">
              <Link href="https://github.com/yourusername/allinonetools" target="_blank" rel="noreferrer">
                <Github className="h-4 w-4 mr-2" />
                GitHub
              </Link>
            </Button>
          </div>
        </MotionEffect>
      )}
    </header>
  );
}