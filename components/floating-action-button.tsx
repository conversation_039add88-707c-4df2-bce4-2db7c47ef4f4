"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  PlusIcon, 
  XIcon, 
  ReceiptIcon, 
  FileTextIcon, 
  RocketIcon,
  StarIcon 
} from "lucide-react";
import Link from "next/link";
import { MotionEffect } from "@/components/animate-ui/effects/motion-effect";
import { motion, AnimatePresence } from "motion/react";

const quickActions = [
  {
    id: "invoice",
    name: "Invoice Generator",
    icon: ReceiptIcon,
    href: "/business/invoice-generator",
    description: "Create professional invoices"
  },
  {
    id: "quote",
    name: "Quote Generator",
    icon: FileTextIcon,
    href: "/business/quote-generator",
    description: "Generate business quotes"
  },
  {
    id: "tools",
    name: "All Tools",
    icon: RocketIcon,
    href: "#tools",
    description: "Browse all available tools"
  }
];

export function FloatingActionButton() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="fixed bottom-6 right-6 z-50">
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="absolute bottom-16 right-0 space-y-3"
          >
            {quickActions.map((action, index) => {
              const Icon = action.icon;
              return (
                <motion.div
                  key={action.id}
                  initial={{ opacity: 0, x: 20, y: 20 }}
                  animate={{ opacity: 1, x: 0, y: 0 }}
                  exit={{ opacity: 0, x: 20, y: 20 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-center gap-3"
                >
                  <div className="bg-background border rounded-lg px-3 py-2 shadow-md">
                    <p className="text-sm font-medium whitespace-nowrap text-foreground">
                      {action.description}
                    </p>
                  </div>
                  <Button
                    asChild
                    size="icon"
                    variant="default"
                    className="h-12 w-12 rounded-full shadow-md hover:shadow-lg transition-shadow"
                  >
                    <Link href={action.href}>
                      <Icon className="h-5 w-5" />
                      <span className="sr-only">{action.name}</span>
                    </Link>
                  </Button>
                </motion.div>
              );
            })}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main FAB */}
      <Button
        size="icon"
        onClick={() => setIsOpen(!isOpen)}
        className="h-14 w-14 rounded-full shadow-lg hover:shadow-xl bg-primary hover:bg-primary/90 text-primary-foreground transition-all duration-200"
      >
        <motion.div
          animate={{ rotate: isOpen ? 45 : 0 }}
          transition={{ duration: 0.2 }}
        >
          {isOpen ? (
            <XIcon className="h-6 w-6" />
          ) : (
            <PlusIcon className="h-6 w-6" />
          )}
        </motion.div>
      </Button>
    </div>
  );
}
