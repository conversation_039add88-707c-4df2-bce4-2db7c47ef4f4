'use client'

import { motion } from 'motion/react';
import { useEffect, useRef, useCallback, useMemo } from 'react';
import Link from 'next/link';
import { RocketIcon, ZapIcon, ShieldCheckIcon, ArrowRightIcon } from 'lucide-react';
import { gsap } from 'gsap';

import { HoleBackground } from '@/components/animate-ui/backgrounds/hole';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { toolCategories } from '@/lib/tools-config';
import { cn } from '@/lib/utils';

// Get all tools for floating animation
const allTools = toolCategories.flatMap(category => category.tools);

// Define positions for floating tools around the hole
const toolPositions = [
  // Top area - avoiding the center hole
  { x: '12%', y: '15%', delay: 0.2, orbit: 'outer' },
  { x: '88%', y: '18%', delay: 0.4, orbit: 'outer' },
  { x: '20%', y: '12%', delay: 0.6, orbit: 'outer' },
  { x: '80%', y: '15%', delay: 0.8, orbit: 'outer' },

  // Middle sides - further from center
  { x: '5%', y: '40%', delay: 1.0, orbit: 'middle' },
  { x: '95%', y: '45%', delay: 1.2, orbit: 'middle' },
  { x: '8%', y: '60%', delay: 1.4, orbit: 'middle' },
  { x: '92%', y: '65%', delay: 1.6, orbit: 'middle' },

  // Bottom area - below the hole
  { x: '15%', y: '85%', delay: 1.8, orbit: 'bottom' },
  { x: '85%', y: '88%', delay: 2.0, orbit: 'bottom' },

  // Additional positions for more tools
  { x: '30%', y: '8%', delay: 2.2, orbit: 'outer' },
  { x: '70%', y: '10%', delay: 2.4, orbit: 'outer' },
];

interface FloatingToolProps {
  tool: typeof allTools[0];
  position: typeof toolPositions[0];
  index: number;
}

function FloatingTool({ tool, position, index }: FloatingToolProps) {
  const Icon = tool.icon;
  const toolRef = useRef<HTMLDivElement>(null);

  // Memoize parsed position values
  const { startX, startY, distanceFromCenter, suckDelay } = useMemo(() => {
    const x = parseFloat(position.x);
    const y = parseFloat(position.y);
    const distance = Math.sqrt(Math.pow(x - 50, 2) + Math.pow(y - 45, 2));
    const delay = 1 + (distance / 100) * 0.5 + index * 0.05;

    return {
      startX: x,
      startY: y,
      distanceFromCenter: distance,
      suckDelay: delay
    };
  }, [position.x, position.y, index]);

  // Memoize center position calculation
  const calculateCenterPosition = useCallback(() => {
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // Hole center is at 50% width, 45% height
    const centerX = viewportWidth * 0.5;
    const centerY = viewportHeight * 0.6;

    // Current tool position in pixels
    const currentX = viewportWidth * (startX / 100);
    const currentY = viewportHeight * (startY / 100);

    // Calculate exact distance to move to center
    return {
      deltaX: centerX - currentX,
      deltaY: centerY - currentY
    };
  }, [startX, startY]);





  useEffect(() => {
    const element = toolRef.current;
    if (!element) return;

    // Set initial state - tools are visible and in position
    gsap.set(element, {
      opacity: 1,
      scale: 1,
      rotation: 0,
      x: 0,
      y: 0,
      // Performance optimizations
      force3D: true,
      transformOrigin: "center center"
    });

    // Sucking animation - tools get pulled into the hole and disappear
    const tl = gsap.timeline({
      delay: suckDelay,
      onStart: () => {
        // Add will-change for performance
        if (element) {
          element.style.willChange = 'transform, opacity';
        }
      },
      onComplete: () => {
        // Remove will-change after animation
        if (element) {
          element.style.willChange = 'auto';
        }
      }
    });

    // Calculate movement on animation start for accuracy
    const { deltaX, deltaY } = calculateCenterPosition();

    // Single smooth continuous animation - no pauses
    tl.to(element, {
      x: deltaX, // Move directly to center
      y: deltaY,
      scale: 0, // Shrink to nothing
      rotation: 720 * (index % 2 === 0 ? 1 : -1), // Multiple rotations
      opacity: 0, // Fade out
      duration: 2.0, // Single long duration for smooth flow
      ease: "power2.inOut", // Smooth acceleration and deceleration
      force3D: true,
      transformOrigin: "center center"
    });

    // Optimized resize handler with throttling
    let resizeTimeout: NodeJS.Timeout;
    const handleResize = () => {
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(() => {
        if (tl.isActive()) {
          tl.kill();
          const { deltaX: newDeltaX, deltaY: newDeltaY } = calculateCenterPosition();
          // Smooth continuous animation for resize
          gsap.to(element, {
            x: newDeltaX,
            y: newDeltaY,
            scale: 0,
            rotation: 720 * (index % 2 === 0 ? 1 : -1),
            opacity: 0,
            duration: 1.5,
            ease: "power2.inOut",
            force3D: true
          });
        }
      }, 100); // Throttle resize events
    };

    window.addEventListener('resize', handleResize, { passive: true });

    // Cleanup
    return () => {
      tl.kill();
      clearTimeout(resizeTimeout);
      window.removeEventListener('resize', handleResize);
    };
  }, [index, startX, startY, tool.name, suckDelay, calculateCenterPosition]);

  // Subtle hover effects that don't interfere with physics
  const handleMouseEnter = useCallback(() => {
    if (!toolRef.current) return;
    gsap.to(toolRef.current, {
      scale: 1.1,
      rotation: "+=5",
      duration: 0.2,
      ease: "power2.out",
      force3D: true
    });
  }, []);

  const handleMouseLeave = useCallback(() => {
    if (!toolRef.current) return;
    gsap.to(toolRef.current, {
      scale: 1,
      rotation: "-=5",
      duration: 0.2,
      ease: "power2.out",
      force3D: true
    });
  }, []);

  return (
    <div
      ref={toolRef}
      className="absolute z-10 cursor-pointer"
      style={{
        left: `${startX}vw`,
        top: `${startY}vh`,
        transformOrigin: 'center center',
        willChange: 'transform, opacity', // Performance hint
        backfaceVisibility: 'hidden', // Prevent flickering
        perspective: 1000 // Enable 3D acceleration
      }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
        <Link href={tool.enabled ? tool.href : '#'}>
          <div className={cn(
            "relative group cursor-pointer",
            !tool.enabled && "cursor-not-allowed"
          )}>
            {/* Enhanced glow effect */}
            <div className={cn(
              "absolute inset-0 rounded-full blur-xl transition-all duration-500",
              tool.enabled
                ? "bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 opacity-40 group-hover:opacity-70 group-hover:scale-110"
                : "bg-gray-400 opacity-20"
            )} />

            {/* Secondary glow */}
            <div className={cn(
              "absolute inset-0 rounded-full blur-md transition-all duration-300",
              tool.enabled
                ? "bg-gradient-to-r from-cyan-400 to-blue-500 opacity-30 group-hover:opacity-50"
                : "bg-gray-400 opacity-10"
            )} />

            {/* Tool icon container */}
            <div className={cn(
              "relative w-16 h-16 md:w-20 md:h-20 lg:w-24 lg:h-24 rounded-full flex items-center justify-center",
              "bg-white/20 backdrop-blur-md border-2 border-white/40",
              "shadow-2xl transition-all duration-300",
              tool.enabled
                ? "text-white"
                : "text-white/40"
            )}>
              <Icon className="w-8 h-8 md:w-10 md:h-10 lg:w-12 lg:h-12" />
            </div>

            {/* Tool name tooltip */}
            <div className="absolute -bottom-10 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none">
              <div className="bg-black/90 backdrop-blur-sm text-white text-xs px-3 py-2 rounded-lg whitespace-nowrap shadow-xl border border-white/20">
                {tool.name}
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1 w-2 h-2 bg-black/90 rotate-45" />
              </div>
            </div>

            {/* Status indicator */}
            {tool.enabled && (
              <div className="absolute -top-2 -right-2 w-5 h-5 bg-green-500 rounded-full border-2 border-white shadow-lg">
                <div className="w-full h-full bg-green-400 rounded-full animate-ping opacity-75" />
              </div>
            )}

            {/* Coming soon indicator */}
            {!tool.enabled && (
              <div className="absolute -top-2 -right-2 w-5 h-5 bg-amber-500 rounded-full border-2 border-white shadow-lg">
                <div className="w-full h-full bg-amber-400 rounded-full animate-pulse" />
              </div>
            )}
          </div>
        </Link>
    </div>
  );
}

export function HoleHero() {
  // Optimize by limiting tools and memoizing the list
  const optimizedTools = useMemo(() => {
    // Show maximum 12 tools for performance (enough for dramatic effect)
    return allTools.slice(0, 12);
  }, []);

  return (
    <div className="relative min-h-screen overflow-hidden">
      {/* Hole Background */}
      <HoleBackground
        className="absolute inset-0"
        strokeColor="#4f46e5"
        numberOfLines={40}
        numberOfDiscs={30}
        particleRGBColor={[79, 70, 229]}
      >
        {/* Floating Tools - Optimized for performance */}
        {optimizedTools.map((tool, index) => {
          const positionIndex = index % toolPositions.length;
          return (
            <FloatingTool
              key={`${tool.id}-${index}`} // More stable key
              tool={tool}
              position={toolPositions[positionIndex]}
              index={index}
            />
          );
        })}
        
        {/* Hero Content */}
        <div className="relative z-20 flex items-center justify-center min-h-screen px-4">
          <div className="max-w-4xl mx-auto text-center">
            {/* Badge - Appears after all tools are sucked in */}
            <motion.div
              initial={{ opacity: 0, scale: 0, y: 0 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              transition={{
                duration: 0.8,
                delay: 3.5, // After all tools are sucked in
                ease: "backOut"
              }}
              className="mb-8"
            >
              <Badge
                variant="secondary"
                className="px-6 py-3 text-sm font-medium bg-white/10 backdrop-blur-md border border-white/20 text-white hover:bg-white/20 transition-colors"
              >
                <ZapIcon className="w-4 h-4 mr-2" />
                Free • No Login Required • Instant Access
              </Badge>
            </motion.div>

            {/* Main Heading - Emerges from center after tools are collected */}
            <motion.h1
              initial={{ opacity: 0, scale: 0.3 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{
                duration: 1.2,
                delay: 3.8, // After tools are sucked in
                ease: "backOut"
              }}
              className="text-4xl md:text-6xl lg:text-7xl xl:text-8xl font-bold text-white mb-6 leading-tight"
            >
              <motion.span
                className="block"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 4.0 }}
              >
                All-in-One Tools
              </motion.span>
              <motion.span
                className="block bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent"
                initial={{ opacity: 0, y: 30 }}
                animate={{
                  opacity: 1,
                  y: 0,
                  backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],
                }}
                transition={{
                  opacity: { duration: 0.8, delay: 4.2 },
                  y: { duration: 0.8, delay: 4.2 },
                  backgroundPosition: {
                    duration: 3,
                    repeat: Infinity,
                    ease: "linear",
                    delay: 5
                  }
                }}
                style={{
                  backgroundSize: '200% 200%'
                }}
              >
                Collection
              </motion.span>
            </motion.h1>

            {/* Subtitle - Emphasizes the collection concept */}
            <motion.p
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 4.5 }}
              className="text-lg md:text-xl lg:text-2xl text-white/80 mb-10 max-w-3xl mx-auto leading-relaxed"
            >
              All your essential tools, now collected in one powerful platform.
              From business documents to development utilities - everything unified and ready to use.
            </motion.p>

            {/* CTA Buttons - Slide up */}
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 4.8 }}
              className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-16"
            >
              <Button
                size="lg"
                className="px-8 py-4 text-lg font-semibold bg-white text-gray-900 hover:bg-gray-100 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105"
                asChild
              >
                <Link href="#tools" className="flex items-center gap-2">
                  <span>Browse Tools</span>
                  <RocketIcon className="w-5 h-5" />
                </Link>
              </Button>

              <Button
                size="lg"
                variant="outline"
                className="px-8 py-4 text-lg font-semibold bg-white/10 backdrop-blur-md border border-white/20 text-white hover:bg-white/20 transition-all duration-300"
                asChild
              >
                <Link href="/business/invoice-generator" className="flex items-center gap-2">
                  <span>Try Invoice Generator</span>
                  <ArrowRightIcon className="w-5 h-5" />
                </Link>
              </Button>
            </motion.div>

            {/* Feature Highlights - Staggered animation */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 5.2 }}
              className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-3xl mx-auto"
            >
              <motion.div
                className="text-center p-6 bg-white/5 backdrop-blur-md rounded-xl border border-white/10"
                initial={{ opacity: 0, y: 30, scale: 0.8 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.6, delay: 5.4, ease: "backOut" }}
              >
                <motion.div
                  className="w-12 h-12 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-4"
                  initial={{ scale: 0, rotate: -180 }}
                  animate={{ scale: 1, rotate: 0 }}
                  transition={{ duration: 0.5, delay: 5.6, ease: "backOut" }}
                >
                  <ZapIcon className="w-6 h-6 text-blue-400" />
                </motion.div>
                <h3 className="font-semibold text-white mb-2">Instant Access</h3>
                <p className="text-sm text-white/70">No registration required. Start using tools immediately.</p>
              </motion.div>

              <motion.div
                className="text-center p-6 bg-white/5 backdrop-blur-md rounded-xl border border-white/10"
                initial={{ opacity: 0, y: 30, scale: 0.8 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.6, delay: 5.6, ease: "backOut" }}
              >
                <motion.div
                  className="w-12 h-12 bg-purple-500/20 rounded-full flex items-center justify-center mx-auto mb-4"
                  initial={{ scale: 0, rotate: -180 }}
                  animate={{ scale: 1, rotate: 0 }}
                  transition={{ duration: 0.5, delay: 5.8, ease: "backOut" }}
                >
                  <ShieldCheckIcon className="w-6 h-6 text-purple-400" />
                </motion.div>
                <h3 className="font-semibold text-white mb-2">Secure & Private</h3>
                <p className="text-sm text-white/70">Your data stays private. No tracking or data collection.</p>
              </motion.div>

              <motion.div
                className="text-center p-6 bg-white/5 backdrop-blur-md rounded-xl border border-white/10"
                initial={{ opacity: 0, y: 30, scale: 0.8 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.6, delay: 5.8, ease: "backOut" }}
              >
                <motion.div
                  className="w-12 h-12 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4"
                  initial={{ scale: 0, rotate: -180 }}
                  animate={{ scale: 1, rotate: 0 }}
                  transition={{ duration: 0.5, delay: 6.0, ease: "backOut" }}
                >
                  <RocketIcon className="w-6 h-6 text-green-400" />
                </motion.div>
                <h3 className="font-semibold text-white mb-2">Professional Quality</h3>
                <p className="text-sm text-white/70">Enterprise-grade tools for professional results.</p>
              </motion.div>
            </motion.div>
          </div>
        </div>

        {/* Scroll Indicator - Appears last */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 6.5 }}
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20"
        >
          <motion.div
            animate={{ y: [0, 10, 0] }}
            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
            className="flex flex-col items-center text-white/60 hover:text-white/80 transition-colors cursor-pointer"
            onClick={() => {
              const toolsSection = document.getElementById('tools');
              toolsSection?.scrollIntoView({ behavior: 'smooth' });
            }}
          >
            <span className="text-sm mb-2 font-medium">Explore Tools</span>
            <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
              <motion.div
                animate={{ y: [0, 12, 0] }}
                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                className="w-1 h-3 bg-white/60 rounded-full mt-2"
              />
            </div>
          </motion.div>
        </motion.div>
      </HoleBackground>
    </div>
  );
}
