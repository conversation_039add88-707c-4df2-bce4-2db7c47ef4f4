"use client";

import { 
  ZapIcon, 
  ShieldCheckIcon, 
  DownloadIcon, 
  PaletteIcon,
  ClockIcon,
  GlobeIcon,
  CheckCircleIcon,
  StarIcon
} from "lucide-react";
import { RevealOnScroll } from "@/components/gsap/reveal-on-scroll";
import { MotionEffect } from "@/components/animate-ui/effects/motion-effect";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

const features = [
  {
    icon: ZapIcon,
    title: "Lightning Fast",
    description: "Instant tool loading with no delays. Get your work done in seconds, not minutes.",
    highlight: "< 1s load time",
    color: "text-yellow-500",
    bgColor: "bg-yellow-500/10",
  },
  {
    icon: ShieldCheckIcon,
    title: "Privacy First",
    description: "Your data never leaves your browser. No tracking, no storage, complete privacy.",
    highlight: "100% Private",
    color: "text-green-500",
    bgColor: "bg-green-500/10",
  },
  {
    icon: DownloadIcon,
    title: "Export Ready",
    description: "Professional PDF exports, multiple formats, and high-quality outputs for all tools.",
    highlight: "PDF Export",
    color: "text-blue-500",
    bgColor: "bg-blue-500/10",
  },
  {
    icon: PaletteIcon,
    title: "Customizable",
    description: "Personalize templates, colors, and layouts to match your brand and style.",
    highlight: "Your Brand",
    color: "text-purple-500",
    bgColor: "bg-purple-500/10",
  },
  {
    icon: ClockIcon,
    title: "Always Available",
    description: "24/7 access to all tools. No maintenance windows, no downtime, always ready.",
    highlight: "99.9% Uptime",
    color: "text-orange-500",
    bgColor: "bg-orange-500/10",
  },
  {
    icon: GlobeIcon,
    title: "Works Everywhere",
    description: "Cross-platform compatibility. Works on desktop, tablet, and mobile devices.",
    highlight: "All Devices",
    color: "text-cyan-500",
    bgColor: "bg-cyan-500/10",
  },
];

const benefits = [
  "No software installation required",
  "No subscription fees or hidden costs",
  "Professional-grade results",
  "Regular updates and new features",
  "Responsive customer support",
  "Mobile-friendly interface",
];

export function FeaturesSection() {
  return (
    <section className="py-20  lg:py-32 bg-muted/30">
      <div className="px-4 container mx-auto sm:px-6 lg:px-12">
        {/* Header */}
        <RevealOnScroll
          effect="blurIn"
          className="text-center mb-16"
          toVars={{ duration: 1, delay: 0.2 }}
        >
          <Badge variant="secondary" className="mb-4">
            <StarIcon className="w-4 h-4 mr-2" />
            Why Choose Our Tools
          </Badge>
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-6">
            Built for Modern Professionals
          </h2>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
            Every tool is designed with your productivity in mind. Fast, secure, and reliable - 
            exactly what you need to get your work done efficiently.
          </p>
        </RevealOnScroll>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-20">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <MotionEffect
                key={index}
                fade
                slide={{ direction: "up", offset: 50 }}
                delay={index * 0.1}
                inView
                inViewMargin="-100px"
              >
                <Card className="h-full group hover:shadow-md transition-all duration-200 border hover:border-primary/30">
                  <CardHeader>
                    <div className="flex items-center justify-between mb-4">
                      <div className={`p-3 rounded-lg ${feature.bgColor} transition-colors`}>
                        <Icon className={`h-6 w-6 ${feature.color}`} />
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {feature.highlight}
                      </Badge>
                    </div>
                    <CardTitle className="text-xl group-hover:text-primary transition-colors">
                      {feature.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground leading-relaxed">
                      {feature.description}
                    </p>
                  </CardContent>
                </Card>
              </MotionEffect>
            );
          })}
        </div>

        {/* Benefits Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <MotionEffect
            fade
            slide={{ direction: "left", offset: 50 }}
            delay={0.2}
            inView
          >
            <div>
              <h3 className="text-2xl sm:text-3xl font-bold mb-6">
                Everything You Need, Nothing You Don't
              </h3>
              <p className="text-muted-foreground mb-8 text-lg">
                We've eliminated the complexity and focused on what matters most - 
                helping you create professional results quickly and easily.
              </p>
              <div className="space-y-3">
                {benefits.map((benefit, index) => (
                  <MotionEffect
                    key={index}
                    fade
                    slide={{ direction: "left", offset: 30 }}
                    delay={0.3 + index * 0.1}
                    inView
                    className="flex items-center gap-3"
                  >
                    <CheckCircleIcon className="h-5 w-5 text-green-500 flex-shrink-0" />
                    <span className="text-foreground">{benefit}</span>
                  </MotionEffect>
                ))}
              </div>
            </div>
          </MotionEffect>

          <MotionEffect
            fade
            slide={{ direction: "right", offset: 50 }}
            delay={0.4}
            inView
            className="relative"
          >
            <div className="relative bg-gradient-to-br from-primary/10 to-secondary/10 rounded-2xl p-8 lg:p-12">
              <div className="absolute top-4 right-4">
                <Badge variant="default" className="bg-green-500">
                  <span className="w-2 h-2 bg-white rounded-full mr-2 animate-pulse" />
                  Live
                </Badge>
              </div>
              
              <div className="space-y-6">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center">
                    <ZapIcon className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h4 className="font-semibold">Ready to Use</h4>
                    <p className="text-sm text-muted-foreground">Start creating immediately</p>
                  </div>
                </div>
                
                <div className="bg-background/50 rounded-lg p-4 border">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Tool Performance</span>
                    <span className="text-sm text-green-500">Excellent</span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div className="bg-green-500 h-2 rounded-full w-[95%]"></div>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-primary">10K+</div>
                    <div className="text-xs text-muted-foreground">Happy Users</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-primary">50K+</div>
                    <div className="text-xs text-muted-foreground">Documents</div>
                  </div>
                </div>
              </div>
            </div>
          </MotionEffect>
        </div>
      </div>
    </section>
  );
}
