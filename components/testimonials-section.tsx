"use client";

import { MapPinIcon, StarIcon, BriefcaseIcon, CodeIcon, ImageIcon } from "lucide-react";
import { ProfilePeek } from "@/components/gsap/profile-peek";
import { RevealOnScroll } from "@/components/gsap/reveal-on-scroll";
import { MotionEffect } from "@/components/animate-ui/effects/motion-effect";

const testimonials = [
  {
    image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
    name: "<PERSON>",
    role: "Small Business Owner",
    company: "Thompson Consulting",
    comment: "The invoice generator saved me hours every week. Clean, professional invoices that my clients love. No more expensive software subscriptions!",
    location: "San Francisco, CA",
    toolUsed: "Invoice Generator",
    icon: BriefcaseIcon,
  },
  {
    image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
    name: "<PERSON>",
    role: "Freelance Designer",
    company: "Creative Studio",
    comment: "I use multiple tools here daily. The quote generator helps me send professional proposals, and everything just works perfectly.",
    location: "Austin, TX",
    toolUsed: "Quote Generator",
    icon: ImageIcon,
  },
  {
    image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
    name: "David Chen",
    role: "Software Developer",
    company: "Tech Startup",
    comment: "Finally, a collection of tools that doesn't require signing up for everything. Fast, reliable, and exactly what I need for my projects.",
    location: "Seattle, WA",
    toolUsed: "Development Tools",
    icon: CodeIcon,
  },
  {
    image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
    name: "Sarah Johnson",
    role: "Marketing Manager",
    company: "Growth Agency",
    comment: "These tools have become essential for our team. Professional results without the complexity. Highly recommend to anyone looking for efficiency.",
    location: "New York, NY",
    toolUsed: "Business Tools",
    icon: BriefcaseIcon,
  },
];

export function TestimonialsSection() {
  return (
    <section className="relative overflow-hidden py-20 lg:py-32">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-background to-secondary/5" />
      
      <div className="relative px-4 sm:px-6 lg:px-12">
        {/* Header */}
        <RevealOnScroll
          effect="blurIn"
          className="text-center mb-16"
          toVars={{ duration: 1, delay: 0.2 }}
        >
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-4">
            Trusted by Professionals
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Join thousands of users who rely on our tools for their daily work
          </p>
        </RevealOnScroll>

        {/* Stats */}
        <MotionEffect
          fade
          slide={{ direction: "up", offset: 30 }}
          delay={0.3}
          inView
          className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16 max-w-3xl mx-auto"
        >
          <div className="text-center">
            <div className="text-3xl font-bold text-primary mb-2">10K+</div>
            <div className="text-sm text-muted-foreground">Active Users</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-primary mb-2">50K+</div>
            <div className="text-sm text-muted-foreground">Documents Created</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-primary mb-2">99.9%</div>
            <div className="text-sm text-muted-foreground">Uptime</div>
          </div>
        </MotionEffect>

        {/* Testimonials */}
        <div className="flex items-center justify-center mb-8">
          <p className="text-lg font-medium text-foreground/70 sm:text-center">
            What our users say
          </p>
        </div>
        
        <div className="flex items-center justify-center -space-x-4 sm:-space-x-6">
          {testimonials.map((testimonial, index) => {
            const Icon = testimonial.icon;
            return (
              <MotionEffect
                key={index}
                fade
                slide={{ direction: "up", offset: 20 }}
                delay={index * 0.1}
                inView
              >
                <ProfilePeek
                  trigger={
                    <div className="relative group cursor-pointer">
                      <img
                        src={testimonial.image}
                        alt={testimonial.name}
                        className="size-16 sm:size-20 rounded-full border-4 border-background object-cover shadow-lg group-hover:scale-110 transition-transform duration-300"
                      />
                      <div className="absolute -bottom-1 -right-1 size-6 bg-primary rounded-full flex items-center justify-center border-2 border-background">
                        <Icon className="size-3 text-primary-foreground" />
                      </div>
                    </div>
                  }
                  content={
                    <div className="bg-card w-80 sm:w-96 rounded-xl p-6 shadow-xl border">
                      <div className="flex items-start gap-4">
                        <img
                          src={testimonial.image}
                          alt={testimonial.name}
                          className="size-12 rounded-full object-cover"
                        />
                        <div className="flex-1">
                          <h3 className="font-semibold text-lg">{testimonial.name}</h3>
                          <p className="text-foreground/60 text-sm">{testimonial.role}</p>
                          <p className="text-foreground/50 text-xs">{testimonial.company}</p>
                        </div>
                      </div>
                      
                      <blockquote className="text-foreground/80 mt-4 text-sm leading-relaxed italic">
                        "{testimonial.comment}"
                      </blockquote>
                      
                      <div className="flex items-center justify-between mt-4 pt-4 border-t">
                        <div className="flex items-center gap-1.5 text-foreground/60 text-xs">
                          <MapPinIcon className="size-3" />
                          <span>{testimonial.location}</span>
                        </div>
                        <div className="flex items-center gap-0.5">
                          {[...Array(5)].map((_, i) => (
                            <StarIcon key={i} className="size-3 text-yellow-500 fill-current" />
                          ))}
                        </div>
                      </div>
                      
                      <div className="mt-3 text-xs text-primary font-medium">
                        Uses: {testimonial.toolUsed}
                      </div>
                    </div>
                  }
                />
              </MotionEffect>
            );
          })}
        </div>

        {/* Call to action */}
        <MotionEffect
          fade
          slide={{ direction: "up", offset: 30 }}
          delay={0.6}
          inView
          className="text-center mt-16"
        >
          <p className="text-muted-foreground mb-4">
            Ready to boost your productivity?
          </p>
          <a
            href="#tools"
            className="inline-flex items-center gap-2 bg-primary text-primary-foreground px-6 py-3 rounded-lg font-medium hover:bg-primary/90 transition-colors"
          >
            Try Our Tools Free
          </a>
        </MotionEffect>
      </div>
    </section>
  );
}
