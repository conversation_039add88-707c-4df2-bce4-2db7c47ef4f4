import Link from "next/link";
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Tool } from "@/lib/tools-config";
import { ArrowRightIcon, ClockIcon } from "lucide-react";

interface ToolCardProps {
  tool: Tool;
  viewMode?: "grid" | "list";
}

export function ToolCard({ tool, viewMode = "grid" }: ToolCardProps) {
  const Icon = tool.icon;

  if (viewMode === "list") {
    return (
      <Card className={`group transition-all duration-200 hover:shadow-md border ${
        tool.enabled
          ? "hover:border-primary/30 cursor-pointer"
          : "opacity-60 cursor-not-allowed"
      }`}>
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            <div className={`p-2.5 rounded-lg transition-colors ${
              tool.enabled
                ? "bg-primary/10 text-primary"
                : "bg-muted text-muted-foreground"
            }`}>
              <Icon className="h-5 w-5" />
            </div>

            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <h3 className="text-base font-semibold text-foreground mb-1 truncate">
                    {tool.name}
                  </h3>
                  <p className="text-sm text-muted-foreground line-clamp-1">
                    {tool.description}
                  </p>
                </div>
                <div className="flex items-center gap-3 ml-4">
                  {tool.enabled ? (
                    <Badge variant="secondary" className="bg-green-50 text-green-700 border-green-200 dark:bg-green-950/20 dark:text-green-400 dark:border-green-800">
                      <div className="w-1.5 h-1.5 bg-green-500 rounded-full mr-1.5" />
                      Available
                    </Badge>
                  ) : (
                    <Badge variant="secondary" className="bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-950/20 dark:text-amber-400 dark:border-amber-800">
                      <ClockIcon className="w-3 h-3 mr-1" />
                      In Development
                    </Badge>
                  )}
                  {tool.enabled ? (
                    <Button
                      asChild
                      size="sm"
                      variant="outline"
                      className="hover:bg-primary hover:text-primary-foreground transition-colors"
                    >
                      <Link href={tool.href} className="flex items-center gap-1.5">
                        Open
                        <ArrowRightIcon className="w-3 h-3" />
                      </Link>
                    </Button>
                  ) : (
                    <Button
                      disabled
                      size="sm"
                      variant="outline"
                    >
                      Coming Soon
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`group h-full transition-all duration-200 hover:shadow-lg border ${
      tool.enabled
        ? "hover:border-primary/30 cursor-pointer"
        : "opacity-60 cursor-not-allowed"
    }`}>
      <CardHeader className="pb-4">
        <div className="flex items-start justify-between mb-3">
          <div className={`p-2.5 rounded-lg transition-colors ${
            tool.enabled
              ? "bg-primary/10 text-primary"
              : "bg-muted text-muted-foreground"
          }`}>
            <Icon className="h-5 w-5" />
          </div>
          {tool.enabled ? (
            <Badge variant="secondary" className="bg-green-50 text-green-700 border-green-200 dark:bg-green-950/20 dark:text-green-400 dark:border-green-800">
              <div className="w-1.5 h-1.5 bg-green-500 rounded-full mr-1.5" />
              Available
            </Badge>
          ) : (
            <Badge variant="secondary" className="bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-950/20 dark:text-amber-400 dark:border-amber-800">
              <ClockIcon className="w-3 h-3 mr-1" />
              In Development
            </Badge>
          )}
        </div>
        <div>
          <CardTitle className="text-lg font-semibold text-foreground mb-2">
            {tool.name}
          </CardTitle>
          <CardDescription className="text-sm text-muted-foreground leading-relaxed">
            {tool.description}
          </CardDescription>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        {tool.enabled ? (
          <Button
            asChild
            className="w-full hover:bg-primary hover:text-primary-foreground transition-colors"
            variant="outline"
          >
            <Link href={tool.href} className="flex items-center justify-center gap-2">
              Open Tool
              <ArrowRightIcon className="w-4 h-4" />
            </Link>
          </Button>
        ) : (
          <Button
            disabled
            className="w-full"
            variant="outline"
          >
            Coming Soon
          </Button>
        )}
      </CardContent>
    </Card>
  );
}