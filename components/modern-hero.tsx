"use client";

import { RocketIcon, GlobeIcon, ZapIcon, ShieldCheckIcon } from "lucide-react";
import Link from "next/link";

import { RevealOnScroll } from "@/components/gsap/reveal-on-scroll";
import { RevealText } from "@/components/gsap/reveal-text";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { MotionEffect } from "@/components/animate-ui/effects/motion-effect";

export function ModernHero() {
  return (
    <div className="relative overflow-hidden bg-background">
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-background to-secondary/5" />
      
      {/* Hero content */}
      <div className="relative  pt-12 sm:pt-20 lg:pt-28 2xl:pt-32">
        <div className="flex items-center justify-center">
          <div className="max-w-4xl">
            {/* Badge */}
            <RevealOnScroll
              effect="blurIn"
              className="flex justify-center mb-6"
              toVars={{ duration: 0.8, delay: 0.1 }}
            >
              <Badge variant="secondary" className="px-4 py-2 text-sm font-medium">
                <ZapIcon className="w-4 h-4 mr-2" />
                Free • No Login Required • Instant Access
              </Badge>
            </RevealOnScroll>

            {/* Main heading */}
            <RevealText
              type="lines"
              gsapVars={{
                filter: "blur(8px)",
                duration: 1.75,
                stagger: 0.2,
                yPercent: 50,
                ease: "power4.out",
              }}
            >
              <h1 className="text-center text-4xl leading-[1.15] font-bold tracking-tight md:text-6xl xl:text-7xl 2xl:text-8xl">
                All-in-One Tools
                <span className="block text-primary">Collection</span>
              </h1>
            </RevealText>

            {/* Subtitle */}
            <RevealText
              type="lines"
              className="mt-6"
              gsapVars={{ 
                filter: "blur(8px)", 
                duration: 1.5, 
                stagger: 0.15, 
                delay: 0.25 
              }}
            >
              <p className="text-foreground/70 text-center text-lg leading-relaxed font-medium md:text-xl lg:text-2xl max-w-3xl mx-auto">
                Boost your productivity with our comprehensive collection of free online tools. 
                From business documents to development utilities - everything you need in one place.
              </p>
            </RevealText>

            {/* CTA Buttons */}
            <RevealOnScroll
              effect="blurIn"
              className="mt-10 flex items-center justify-center gap-4 md:gap-6"
              toVars={{ duration: 1, delay: 0.5 }}
            >
              <Button
                size="lg"
                className="px-8 py-3 shadow-lg hover:shadow-xl transition-all duration-200"
                asChild
              >
                <Link href="#tools" className="flex items-center gap-2">
                  <span className="font-semibold">Browse Tools</span>
                  <RocketIcon className="w-4 h-4" />
                </Link>
              </Button>

              <Button
                size="lg"
                variant="outline"
                className="px-8 py-3 border-2 hover:bg-muted transition-colors"
                asChild
              >
                <Link href="/business/invoice-generator" className="flex items-center gap-2">
                  <span className="font-semibold">Try Invoice Generator</span>
                  <GlobeIcon className="w-4 h-4" />
                </Link>
              </Button>
            </RevealOnScroll>

            {/* Feature highlights */}
            <RevealOnScroll
              effect="blurIn"
              className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-6 max-w-3xl mx-auto"
              toVars={{ duration: 1, delay: 0.7, stagger: 0.2 }}
            >
              <MotionEffect
                fade
                slide={{ direction: "up", offset: 30 }}
                delay={0.1}
                className="text-center p-4"
              >
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-3">
                  <ZapIcon className="w-6 h-6 text-primary" />
                </div>
                <h3 className="font-semibold mb-2">Instant Access</h3>
                <p className="text-sm text-muted-foreground">No registration required. Start using tools immediately.</p>
              </MotionEffect>

              <MotionEffect
                fade
                slide={{ direction: "up", offset: 30 }}
                delay={0.2}
                className="text-center p-4"
              >
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-3">
                  <ShieldCheckIcon className="w-6 h-6 text-primary" />
                </div>
                <h3 className="font-semibold mb-2">Secure & Private</h3>
                <p className="text-sm text-muted-foreground">Your data stays private. No tracking or data collection.</p>
              </MotionEffect>

              <MotionEffect
                fade
                slide={{ direction: "up", offset: 30 }}
                delay={0.3}
                className="text-center p-4"
              >
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-3">
                  <GlobeIcon className="w-6 h-6 text-primary" />
                </div>
                <h3 className="font-semibold mb-2">Professional Quality</h3>
                <p className="text-sm text-muted-foreground">Enterprise-grade tools for professional results.</p>
              </MotionEffect>
            </RevealOnScroll>
          </div>
        </div>
      </div>

      {/* Bottom spacing */}
      <div className="h-24 sm:h-32 lg:h-40" />
    </div>
  );
}
