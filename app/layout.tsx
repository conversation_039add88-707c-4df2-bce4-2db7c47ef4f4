import { FloatingActionButton } from "@/components/floating-action-button";
import { ThemeProvider } from "@/components/theme-provider";
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "All-in-One Tools",
  description: "A collection of useful online tools",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
            <div className="relative flex min-h-screen overflow-x-hidden flex-col">
              {/* <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
                <div className="flex h-16 w-full items-center justify-between px-4 sm:px-6 lg:px-8">
                  <a href="/" className="text-xl font-bold hover:text-primary transition-colors">
                    All-in-One Tools
                  </a>
                  <ThemeToggle />
                </div>
              </header> */}
              <main className="flex-1">
                <div className="w-full ">
                  {children}
                </div>
              </main>
              <FloatingActionButton />
            </div>
        </ThemeProvider>
      </body>
    </html>
  );
}
