"use client";

import { useState, useCallback, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { 
  Copy, 
  FileJson, 
  AlertCircle, 
  CheckCircle2, 
  Minimize2, 
  Maximize2,
  RotateCcw,
  ArrowLeft
} from "lucide-react";
import { toast } from "sonner";
import Link from "next/link";

interface JsonFormatterState {
  input: string;
  output: string;
  error: string | null;
  isValid: boolean;
  isMinified: boolean;
}

export default function JsonFormatterPage() {
  const [state, setState] = useState<JsonFormatterState>({
    input: "",
    output: "",
    error: null,
    isValid: false,
    isMinified: false,
  });

  // Debounced JSON formatting
  const formatJson = useCallback((jsonString: string) => {
    if (!jsonString.trim()) {
      setState(prev => ({
        ...prev,
        output: "",
        error: null,
        isValid: false,
      }));
      return;
    }

    try {
      const parsed = JSON.parse(jsonString);
      const formatted = state.isMinified 
        ? JSON.stringify(parsed)
        : JSON.stringify(parsed, null, 2);
      
      setState(prev => ({
        ...prev,
        output: formatted,
        error: null,
        isValid: true,
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        output: "",
        error: error instanceof Error ? error.message : "Invalid JSON",
        isValid: false,
      }));
    }
  }, [state.isMinified]);

  // Debounce effect
  useEffect(() => {
    const timer = setTimeout(() => {
      formatJson(state.input);
    }, 500);

    return () => clearTimeout(timer);
  }, [state.input, formatJson]);

  const handleInputChange = (value: string) => {
    setState(prev => ({ ...prev, input: value }));
  };

  const handleCopyToClipboard = async () => {
    if (!state.output) return;
    
    try {
      await navigator.clipboard.writeText(state.output);
      toast.success("Copied to clipboard!");
    } catch (error) {
      toast.error("Failed to copy to clipboard");
    }
  };

  const handleToggleMinify = () => {
    setState(prev => ({ ...prev, isMinified: !prev.isMinified }));
  };

  const handleClear = () => {
    setState({
      input: "",
      output: "",
      error: null,
      isValid: false,
      isMinified: false,
    });
  };

  const handleFormatNow = () => {
    formatJson(state.input);
  };

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if ((event.metaKey || event.ctrlKey) && event.key === "Enter") {
        event.preventDefault();
        handleFormatNow();
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [state.input]);

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button variant="ghost" size="sm" asChild>
                <Link href="/" className="flex items-center gap-2">
                  <ArrowLeft className="h-4 w-4" />
                  Back to Tools
                </Link>
              </Button>
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-primary/10 text-primary">
                  <FileJson className="h-5 w-5" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-foreground">JSON Formatter</h1>
                  <p className="text-sm text-muted-foreground">Format and validate JSON data</p>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {state.isValid && (
                <Badge variant="secondary" className="bg-green-50 text-green-700 border-green-200 dark:bg-green-950/20 dark:text-green-400 dark:border-green-800">
                  <CheckCircle2 className="w-3 h-3 mr-1" />
                  Valid JSON
                </Badge>
              )}
              {state.error && (
                <Badge variant="destructive" className="bg-red-50 text-red-700 border-red-200 dark:bg-red-950/20 dark:text-red-400 dark:border-red-800">
                  <AlertCircle className="w-3 h-3 mr-1" />
                  Invalid JSON
                </Badge>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-[calc(100vh-200px)]">
          {/* Input Panel */}
          <Card className="flex flex-col h-full">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg font-semibold">JSON Input</CardTitle>
                  <p className="text-sm text-muted-foreground mt-1">
                    Enter your JSON data below
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleClear}
                    className="flex items-center gap-1.5"
                  >
                    <RotateCcw className="h-3 w-3" />
                    Clear
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="flex-1 flex flex-col">
              <div className="flex-1">
                <Label htmlFor="json-input" className="sr-only">
                  JSON Input
                </Label>
                <Textarea
                  id="json-input"
                  placeholder='Enter JSON here... e.g., {"name": "John", "age": 30}'
                  value={state.input}
                  onChange={(e) => handleInputChange(e.target.value)}
                  className="h-full resize-none font-mono text-sm bg-background border-input focus:border-ring focus:ring-1 focus:ring-ring"
                  style={{ minHeight: "400px" }}
                />
              </div>
              <div className="mt-4 text-xs text-muted-foreground">
                Tip: Use Cmd/Ctrl + Enter to format instantly
              </div>
            </CardContent>
          </Card>

          {/* Output Panel */}
          <Card className="flex flex-col h-full">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg font-semibold">Formatted Output</CardTitle>
                  <p className="text-sm text-muted-foreground mt-1">
                    {state.isMinified ? "Minified JSON" : "Pretty-printed JSON"}
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleToggleMinify}
                    className="flex items-center gap-1.5"
                  >
                    {state.isMinified ? (
                      <>
                        <Maximize2 className="h-3 w-3" />
                        Beautify
                      </>
                    ) : (
                      <>
                        <Minimize2 className="h-3 w-3" />
                        Minify
                      </>
                    )}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCopyToClipboard}
                    disabled={!state.output}
                    className="flex items-center gap-1.5"
                  >
                    <Copy className="h-3 w-3" />
                    Copy
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="flex-1 flex flex-col">
              <div className="flex-1 relative">
                {state.error ? (
                  <div className="h-full flex items-center justify-center">
                    <div className="text-center">
                      <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
                      <h3 className="text-lg font-semibold text-foreground mb-2">Invalid JSON</h3>
                      <p className="text-sm text-muted-foreground mb-4 max-w-md">
                        {state.error}
                      </p>
                      <Button variant="outline" size="sm" onClick={handleFormatNow}>
                        Try Again
                      </Button>
                    </div>
                  </div>
                ) : state.output ? (
                  <div className="h-full">
                    <pre className="h-full overflow-auto p-4 bg-muted/30 rounded-md border font-mono text-sm text-foreground whitespace-pre-wrap break-words">
                      {state.output}
                    </pre>
                  </div>
                ) : (
                  <div className="h-full flex items-center justify-center">
                    <div className="text-center">
                      <FileJson className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-semibold text-foreground mb-2">Ready to Format</h3>
                      <p className="text-sm text-muted-foreground">
                        Enter JSON in the input panel to see formatted output here
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
