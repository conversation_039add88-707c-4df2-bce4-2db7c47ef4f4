import { <PERSON>ada<PERSON> } from "next";
import { Hole<PERSON>ero } from "@/components/hole-hero";
import { TestimonialsSection } from "@/components/testimonials-section";
import { FeaturesSection } from "@/components/features-section";
import { ToolCategories } from "@/components/tool-categories";

export const metadata: Metadata = {
  title: "All-in-One Tools - Free Online Tools Collection",
  description: "A comprehensive collection of free online tools including invoice and quote generators. No login required.",
  keywords: "online tools, invoice generator, quote generator, free tools, no login required",
};

export default function Home() {
  return (
    <div className="relative flex flex-col">
      <main className="flex-1">
        <HoleHero />
        <ToolCategories />
        <FeaturesSection />
        <TestimonialsSection />
        <footer className="border-t">
          <div className="flex h-24 w-full items-center justify-center px-4 sm:px-6 lg:px-8">
            <p className="text-sm text-muted-foreground">
              Built with ❤️. All rights reserved.
            </p>
          </div>
        </footer>
      </main>
    </div>
  );
}
